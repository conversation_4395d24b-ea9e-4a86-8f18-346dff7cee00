import request from '@/utils/request'

// 查询DUTP-DTB-029教材模板列表
export function listTemplate(query) {
  return request({
    url: '/book/template/listForAdmin',
    method: 'get',
    params: query
  })
}

// 查询DUTP-DTB-029教材模板详细
export function getTemplate(templateId) {
  return request({
    url: '/book/template/' + templateId,
    method: 'get'
  })
}

// 新增DUTP-DTB-029教材模板
export function addTemplate(data) {
  return request({
    url: '/book/template',
    method: 'post',
    data: data
  })
}

// 修改DUTP-DTB-029教材模板
export function updateTemplate(data) {
  return request({
    url: '/book/template',
    method: 'put',
    data: data
  })
}

// 删除DUTP-DTB-029教材模板
export function delTemplate(templateId) {
  return request({
    url: '/book/template/' + templateId,
    method: 'delete'
  })
}

// 设为默认
export function editDefault(data) {
  return request({
    url: '/book/template/editDefault',
    method: 'put',
    data: data
  })
}

// 开启或者禁用模板
export function editIsEnable(data) {
  return request({
    url: '/book/template/editIsEnable',
    method: 'put',
    data: data,
  })
}