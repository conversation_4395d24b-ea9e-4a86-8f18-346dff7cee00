<template>
  <div class="app-container">
    <div class="chapter-header">
      <div class="chapter-header-main">
        <div>
          <el-checkbox size="large" v-model="selectAll" :disabled="dataList.length == 0"
            @change="handleSelectAll">全部章节</el-checkbox>
        </div>
        <div class="chapter-header-right">
          <el-button @click="handleDelete" :icon="Delete" text :disabled="dataList.length == 0"
            v-hasPermi="['book:book:del']">删除</el-button>
          <el-button @click="handleExport" text :icon="Discount" :disabled="dataList.length == 0"
            v-hasPermi="['book:book:export']">导出</el-button>
        </div>
      </div>
      <div>
        <el-button type="primary" @click="addChapter" :icon="Plus" v-if="publishStatus == 1 && (stepId == 1 || auditState == null || auditState == 3)"
          v-hasPermi="['book:book:addChapter']">新增章节</el-button>
        <el-button type="primary" @click="handleDragSort" :icon="Sort" v-if="publishStatus == 1 && (stepId == 1 || auditState == null || auditState == 3)"
          v-hasPermi="['book:book:sort']" :disabled="dataList.length == 0">章节顺序</el-button>
        <el-button type="primary" @click="handleRecycle" :icon="Delete"
          v-hasPermi="['book:book:recycle']">回收站</el-button>
        <el-button type="primary" @click="openCaptionStyleDialog" v-if="publishStatus == 1 && (stepId == 1 || auditState == null || auditState == 3)"
          v-hasPermi="['book:book:captionStyle']" :icon="Tools">题注样式</el-button>
        <el-button type="primary" @click="openBookTempalteDialog" v-hasPermi="['book:book:template']"
          :icon="CopyDocument">教材模板</el-button>
      </div>
    </div>

    <div class="chapter-list" v-if="dataList.length > 0">
      <TreeNode :dataList="dataList" ref="treeNode" @refresh="refresh" @edit-chapter="editChapter"
        :isFree="props.isFree" :masterFlag="masterFlag" :bookOrganize="bookOrganize" />
    </div>
    <div class="chapter-list" v-else>
      <el-empty description="暂无数据" />
    </div>

    <CharpterMange ref="chapterMange" @refresh="refresh" :isFree="props.isFree" :masterFlag="masterFlag"
      :bookOrganize="bookOrganize" />

    <!-- 回收站 -->
    <el-dialog title="回收站" v-model="isRecycle" width="30%">
      <div class="recycleList" v-if="recycleList.length > 0">
        <div class="recycleList-item" v-for="item in recycleList" :key="item.chapterId">
          <div class="recycleList-item-header">
            <div>{{ item.updateTime }}</div>
            <div class="recycleList-item-header-right" v-if="publishStatus == 1 &&
            ( stepId == 1 || auditState == null || auditState == 3)" @click="handleRecycleChapter(item)">
              恢复
            </div>
          </div>
          <div class="recycleList-item-content">
            <div>{{ item.chapterName }}</div>
            <div class="recycleList-item-content-right" @click="handleRecycleDetail(item)">
              详情
            </div>
          </div>
        </div>
      </div>

      <div class="recycleList" v-else>
        <el-empty description="暂无数据" />
      </div>
    </el-dialog>

    <!-- 题注样式 -->
    <el-dialog v-model="captionStyleDialog" title="题注样式" width="500px">
      <div>
        <el-form :model="captionStyleForm" label-width="120px">
          <el-form-item label="图：">
            <el-radio-group v-model="captionStyleForm.imageNumberType">
              <el-radio :value="1" size="large">图1</el-radio>
              <el-radio :value="2" size="large">图1-1</el-radio>
              <!--              <el-radio :value="3" size="large">不显示</el-radio>-->
            </el-radio-group>
          </el-form-item>
          <el-form-item label="表：">
            <el-radio-group v-model="captionStyleForm.tableNumberType">
              <el-radio :value="1" size="large">表1</el-radio>
              <el-radio :value="2" size="large">表1-1</el-radio>
              <!--              <el-radio :value="3" size="large">不显示</el-radio>-->
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="captionStyleDialog = false">取消</el-button>
          <el-button type="primary" @click="handleConfirmCaptionStyle">
            更新
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 拖拽排序 -->
    <el-dialog v-model="dragSortDialog" title="拖拽排序" width="500px">
      <div>
        <VueDraggable group="inner" class="list-group" v-model="dragSortDataList">
          <div class="list-group-item" v-for="item in dragSortDataList" :key="item.chapterId">
            {{ item.chapterName }}
          </div>
        </VueDraggable>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dragSortDialog = false">取消</el-button>
          <el-button type="primary" @click="handleConfirmDragSort">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>

    <DeleteModal v-model="deleteModalVisible" @confirm="handleConfirmDelete"></DeleteModal>
    <!-- 教材模板 -->
    <el-dialog v-model="bookTemplateDialog" title="教材模板" width="1200px">
      <Cards>
        <div class="tool">
          <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="100px">
            <el-form-item prop="modal" label="模板名称">
              <el-input v-model="queryParams.modal" placeholder="请输入模板名称" clearable />
            </el-form-item>

            <el-form-item prop="serialNumber" label="序号">
              <el-input v-model="queryParams.serialNumber" placeholder="请输入序号" clearable />
            </el-form-item>

            <el-form-item prop="type" label="分类">
              <el-select v-model="queryParams.type" placeholder="全部" clearable style="width: 120px">
                <el-option v-for="item in bookTemplateTypeList" :key="item.typeId" :label="item.typeName"
                  :value="item.typeId" />
              </el-select>
            </el-form-item>

            <el-form-item prop="themeColor" label="颜色">
              <el-select v-model="queryParams.themeColor" placeholder="全部" clearable style="width: 100px">
                <el-option v-for="item in bookTemplateThemeList" :key="item.value" :value="item.value"
                  :label="item.label" style="display: flex;justify-content: center;align-items: center;">
                  <div :style="`width: 20px;height: 20px;background-color: ${item.label};`" />
                </el-option>
                <template #label="{ label, value }">
                  <div style="display: flex; align-items: center; justify-content: center">
                    <div :style="`width: 20px;height: 20px;background-color:${label};`" />
                  </div>
                </template>
              </el-select>
            </el-form-item>

            <el-form-item prop="sortBy" label="排序方式">
              <el-select v-model="queryParams.sortBy" placeholder="默认" clearable style="width: 120px">
                <el-option v-for="item in bookTemplateSortList" :key="item.value" :label="item.label"
                  :value="item.value" />

              </el-select>
            </el-form-item>

            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              <!-- <el-button icon="Select" @click="updateTemplate">保存选中模板</el-button> -->
            </el-form-item>
          </el-form>
        </div>
      </Cards>
      <Cards>
        <div class="textBookTemplate-content">
          <div v-for="item in templateList" :key="item.templateId" class="textBookTemplate-content-item">
            <div :class="`${item.templateId == activeTemplate ? 'active' : ''}`"></div>
            <div class="textBookTemplate-content-item-img">
              <img :src="item.imgUrl" style="width: 170px; height: 250px" />
              <div class="textBookTemplate-content-item-img-opt-mask">
                <div class="textBookTemplate-content-item-img-opt-mask-bnt" @click="handleUpdateTemplate(item)">
                  选择
                </div>
                <div class="textBookTemplate-content-item-img-opt-mask-bnt" @click="handlePrevieTemplate(item)">
                  预览
                </div>
              </div>
            </div>
            <div class="textBookTemplate-content-item-title" :style="`color:${item.templateId == activeTemplate ? '#2979e9' : ''}`">
              {{ item.serialNumber || ''}}{{ item.modal }}
            </div>
          </div>
        </div>
        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize" @pagination="getList" />
      </Cards>

    </el-dialog>


    <el-dialog v-model="warningVisible" title="警示" :close-on-click-modal="false" :close-on-press-escape="false"
      :show-close="false">
      <span>{{ confirmLoading
            ? '正在保存，请稍后...'
            : '当前章节已有人员正在进行编写，是否要继续进入编写?如进入编写，则前编写人员会被强制退出编辑器，且保存编写的内容。' }}</span>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="onClickCancel">取消</el-button>
          <el-button type="primary" :loading="confirmLoading" @click="onClickConfirm">
            编写内容
          </el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog v-model="warningVisibleStyle" title="警示" :close-on-click-modal="false" :close-on-press-escape="false"
      :show-close="false">
      <span>当前教材正在处于题注更新中，无法进入编辑器。</span>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="onClickCancel">关闭</el-button>
        </div>
      </template>
    </el-dialog>
    <el-image-viewer v-if="showPreviewUrl" :url-list="[showPreviewUrl]" show-progress @close="showPreviewUrl = false" />
  </div>
</template>
<script setup name="book-detail-chapter">
import {
  listChapter,
  delChapter,
  listForRecycle,
  recycleChapter,
  listForSort,
  updateChapterSort,
  exportBookChapter,
} from "@/api/book/chapter.js";
import {
  Sort,
  Plus,
  Delete,
  Tools,
  CopyDocument,
  Discount,
} from "@element-plus/icons-vue";
import {
  editCaptionStyle,
  queryCaptionStyle,
  getBookTemplateList,
  updateBookTemplate,
} from "@/api/book/book.js";
import TreeNode from "./TreeNode.vue";
import CharpterMange from "./charpterMange.vue";
import { VueDraggable } from "vue-draggable-plus";
import { RefreshLeft } from "@element-plus/icons-vue";
import ChapterEditWebSocket from "@/utils/chapterEditWebSocket.js";
import DeleteModal from "@/components/DeleteModal";
import { getToken } from "@/utils/auth";
import { listBookTemplateTypeNoPage } from "@/api/book/bookTemplateType.js";
import { ElMessage, ElMessageBox } from "element-plus";
import { bookTemplateSortList, bookTemplateThemeList } from '@/utils/optionUtil'

const { proxy } = getCurrentInstance();
const props = defineProps({
  bookId: {
    type: String,
    default: "",
  },
  publishStatus: {
    type: Number,
    default: 1,
  },
  stepId: {
    type: String,
    default: 1,
  },
  isFree: {
    type: Boolean,
    default: false,
  },
  masterFlag: {
    type: Number,
    default: 1,
  },
  bookOrganize: {
    type: Number,
    default: 1,
  },
  auditState: {
    type: Number,
    default: null,
  }
});
const emit = defineEmits(["refresh"]);
const loading = ref(false);
const showPreviewUrl = ref(false);
const dragSortDialog = ref(false);
const warningVisible = ref(false);
const warningVisibleStyle = ref(false);
const selectAll = ref(false);
const deleteModalVisible = ref(false);
const dataList = ref([]);
const confirmLoading = ref(false)
const recycleList = ref([]);
const isRecycle = ref(false);
const captionStyleDialog = ref(false);
const bookTemplateDialog = ref(false);
const captionStyleForm = ref({});
const dragSortDataList = ref([]);
const templateList = ref([]);
const websocket = ref(null);
const activeTemplate = ref(null);
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
});
const editChapterId = ref(null);
let heartbeatInterval = null;
const total = ref(0);
const isRe = ref(true);
const bookTemplateTypeList = ref([])
//#region 监听器相关

watch(
  () => props.bookId,
  () => {
    getChapters();
    getBookTemplateTypeList()
  }
);

//#endregion

//#region 获取数据相关

// 获取章节列表
function getChapters() {
  loading.value = true;
  listChapter({ bookId: props.bookId }).then((response) => {
    dataList.value = response.data;
    loading.value = false;
  });
}

// 教材模板分类列表
function getBookTemplateTypeList() {
  listBookTemplateTypeNoPage().then((response) => {
    bookTemplateTypeList.value = response.data
  })
}

//#endregion

//#region websocket相关
onMounted(() => {
  isRe.value = true;
  websocket.value = new ChapterEditWebSocket();
  websocket.value.connect();
  websocket.value.on("message", handleMessage);
  websocket.value.on("open", () => {
    heartbeatInterval = setInterval(sendHeartbeat, 5000);
  });
  websocket.value.on("close", () => {
    if (heartbeatInterval) {
      clearInterval(heartbeatInterval);
    }
    // 延迟 5 秒后重试连接
    setTimeout(reConnect, 5000);
  });
  // websocket.value.on("error", () => {
  //   if (heartbeatInterval) {
  //     clearInterval(heartbeatInterval);
  //   }
  //   // 延迟 5 秒后重试连接
  //   setTimeout(reConnect, 5000);
  // });
});

onBeforeUnmount(() => {
  isRe.value = false;
  websocket.value.disconnect();
});

// 接受消息
function handleMessage(message) {
  // 0成功响应心跳 1当前章节正在编辑中 2当前章节被强制关闭 3同意申请编辑章节 4挤退当前编辑人成功
  if (message.resultCode == 0) {
    // 心跳响应
  } else if (message.resultCode == 1) {
    // 当前章节正在编辑中
    warningVisible.value = true
    confirmLoading.value = false
  } else if (message.resultCode == 2) {
    // 当前章节被强制关闭
    // 发送消息
    websocket.value.sendMessage({
      optCode: 5,
      chapterId: editChapterId.value,
    })
    // warningVisible.value = true
    confirmLoading.value = false
  } else if (message.resultCode == 3) {
    // 同意申请编辑章节
    warningVisible.value = false;
    toEditor();
  } else if (message.resultCode == 4) {
    // 挤退当前编辑人成功
    confirmLoading.value = true;
  } else if (message.resultCode == 5) {
    // 当前章节被强制关闭
    warningVisibleStyle.value = true
  } else if (message.resultCode == 6) {
    warningVisible.value = false
    confirmLoading.value = false
    toEditor();
  }
}

// 跳转编辑器
function toEditor() {
  window.location.href =
    import.meta.env.VITE_APP_EDITOR_URL +
    "editor" +
    "?bookId=" +
    props.bookId +
    "&chapterId=" +
    editChapterId.value +
    "&formType=2" +
    "&token=" +
    getToken();
}
// 重连
function reConnect() {
  if (isRe.value) {
    websocket.value.connect();
  }
}

// 心跳
function sendHeartbeat() {
  const isEditing = false;
  websocket.value.sendMessage({
    optCode: 0,
    chapterId: editChapterId.value,
    isEditing,
  });
}

function editChapter(item) {
  editChapterId.value = item.chapterId;
  websocket.value.sendMessage({
    optCode: 1,
    chapterId: editChapterId.value,
  });
}

function onClickCancel() {
  warningVisible.value = false;
  warningVisibleStyle.value = false;
  confirmLoading.value = false
}

function onClickConfirm() {
  websocket.value.sendMessage({
    optCode: 2,
    chapterId: editChapterId.value,
  });
  confirmLoading.value = true
}
//#endregion

//#region 操作相关

// 点击回收站
function handleRecycle() {
  listForRecycle({ bookId: props.bookId }).then((response) => {
    recycleList.value = response.data;
    isRecycle.value = true;
  });
}

// 点击排序
function handleDragSort() {
  listForSort({ bookId: props.bookId }).then((response) => {
    dragSortDataList.value = response.data;
    dragSortDialog.value = true;
  });
}


// 查询教材模板列表
function getList() {
  // loading.value = true;
  queryParams.value.bookId = props.bookId;
  getBookTemplateList(queryParams.value).then((response) => {
    templateList.value = response.rows;
    templateList.value.forEach((item) => {
      if (item.isUse) {
        activeTemplate.value = item.templateId;
      }
    })
    total.value = response.total;
    bookTemplateDialog.value = true;
    // loading.value = false;
  });
}

// 选中
function handleUpdateTemplate(row) {
  activeTemplate.value = row.templateId;
  updateTemplate()
}

//预览
function handlePrevieTemplate(row) {
  showPreviewUrl.value = row.imgUrl
}

// 确认排序
function handleConfirmDragSort() {
  let sort = 0;
  let chapterSortDataList = dragSortDataList.value.map((item) => {
    sort++;
    return {
      chapterId: item.chapterId,
      sort: sort,
    };
  });
  updateChapterSort(chapterSortDataList).then((response) => {
    dragSortDialog.value = false;
    proxy.$message.success("排序成功");
    refresh();
  });
}

// 查看回收站详情
function handleRecycleDetail(item) {
  // TODO 跳预览
  window.location.href =
    import.meta.env.VITE_APP_EDITOR_URL +
    "chapterDetail" +
    "?bookId=" +
    props.bookId +
    "&chapterId=" +
    item.chapterId +
    "&optType=1" +
    "&formType=2" +
    "&token=" +
    getToken();
}

// 恢复章节
function handleRecycleChapter(item) {
  recycleChapter({
    chapterId: item.chapterId,
  }).then((res) => {
    handleRecycle();
    proxy.$message.success("恢复成功");
    refresh();
  });
}

// 搜索按钮操作
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

// 重置按钮操作
function resetQuery() {
  proxy.resetForm("queryRef");
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
  }
  handleQuery();
}


// 刷新数据
function refresh() {
  getChapters();
  emit("refresh");
}

// 新建章节
function addChapter() {
  proxy.$refs["chapterMange"].show(props.bookId, null);
}

// 确认删除
function handleConfirmDelete(fn) {
  console.log("handleConfirmDelete");
  let selectDataList = proxy.$refs["treeNode"].handleSelect();
  let dataIdList = selectDataList
    .filter((item) => item.chapterStatus == 0 || item.chapterStatus == 3)
    .map((item) => item.chapterId);
  if (dataIdList.length == 0) {
    fn(true);
    proxy.$message.warning("请选择要删除的章节");
    return;
  }
  delChapter(dataIdList).then((response) => {
    fn(true);
    proxy.$message.success("删除成功");
    refresh();
  });
}

// 删除章节
function handleDelete() {
  let selectDataList = proxy.$refs["treeNode"].handleSelect();
  if (selectDataList && selectDataList.length == 0) {
    proxy.$message.warning("请选择要删除的章节");
    return;
  }
  let dataIdList = selectDataList
    .filter((item) => item.chapterStatus == 0 || item.chapterStatus == 3)
    .map((item) => item.chapterId);
  if (dataIdList.length == 0) {
    proxy.$message.warning("已提交或者已通过状态下不允许删除的章节");
    return;
  }
  deleteModalVisible.value = true;
}

// 全选
function handleSelectAll() {
  if (selectAll.value) {
    proxy.$refs["treeNode"].handleCheckAll();
  } else {
    proxy.$refs["treeNode"].handleCancelCheckAll();
  }
}

// 编辑题注
function openCaptionStyleDialog() {
  queryCaptionStyle({ bookId: props.bookId }).then((response) => {
    captionStyleForm.value = response.data;
    captionStyleDialog.value = true;
  });
}

// 编辑模板
function openBookTempalteDialog() {
  getList()
}

// 更新模板
function updateTemplate() {
  if (!activeTemplate.value) {
    return proxy.$message.warning("请选择模板");
  }
  updateBookTemplate({
    bookId: props.bookId,
    templateId: activeTemplate.value,
  }).then((res) => {
    // bookTemplateDialog.value = false;
    getList()
  });
}
// 确认题注
async function handleConfirmCaptionStyle() {

  try {
    await ElMessageBox.confirm(
      '确认更新题注样式？确定后题注样式将会进入任务中心。\n注：题注样式的调整需要一定的时间，会在凌晨2点进行，更新期间编辑器不可进入。',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: false,
      }
    )
    await editCaptionStyle(captionStyleForm.value)
    ElMessage.success('编辑成功')
    captionStyleDialog.value = false
  } catch (error) {
    // 用户取消，无需处理
  }
}

// 导出
function handleExport() {
  console.log("handleConfirmDelete");
  let selectDataList = proxy.$refs["treeNode"].handleSelect();
  let dataIdList = selectDataList
    // .filter((item) => item.chapterStatus == 0 || item.chapterStatus == 3)
    .map((item) => item.chapterId);
  console.log("selectDataList", selectDataList, dataIdList);
  exportBookChapter({
    chapterIdList: dataIdList,
  }).then((res) => {
    //proxy.$message.success("已发起导出任务，请到任务中心查看");
    ElMessageBox.confirm(
    "已发起导出任务，请到任务中心查看",
    "导出章节",
    {
      confirmButtonText: "前往任务中心",
      cancelButtonText: "留在本页",
    }
  )
    .then(() => {
      proxy.$router.push({ path: "/book/task" });
    })
  
  });
}
//#endregion
</script>
<style lang="scss">
.list-group {
  width: 100%;

  .list-group-item {
    width: 100%;
    display: flex;

    align-items: center;
    margin: 10px 0;
    padding: 15px 30px;
    background-color: #0966b4;
    border-radius: 5px;
    color: #fff;
    cursor: pointer;

    &:hover {
      background-color: hsl(207, 100%, 50%);
    }

    &::before {
      content: "";
      width: 20px;
      height: 20px;
      margin-right: 10px;
      background-image: url("@/assets/images/move.svg");
      background-size: cover;
    }
  }
}
</style>
<style lang="scss" scoped>
.chapter {
  .chapter-header {
    padding: 20px 30px;
    background-color: #fff;
    border-bottom: 1px solid #eaeaea;

    .chapter-header-main {
      display: flex;
      align-items: center;
    }
  }
}

.chapter-header {
  display: flex;
  justify-content: space-between;

  .chapter-header-main {
    display: flex;
  }

  .chapter-header-right {
    margin-left: 44px;
  }
}

.chapter-list {
  padding-bottom: 20px;
  background-color: #fff;

  .el-tree-node__content {
    background-color: #eaeaea;
    padding: 20px 30px;
    height: auto;
    display: block;

    .chapter-list-item {
      padding-left: 60px;
      margin-top: -25px;

      .charter-list-header {
        font-size: 16px;
        font-weight: bold;
      }
    }

    .charter-list-content {
      display: flex;
      justify-content: space-between;
    }
  }
}

.recycleList {
  .recycleList-item {
    border: 1px solid #ddd;
    border-radius: 5px;
    margin-bottom: 20px;

    .recycleList-item-header {
      background-color: #daeaf7;
      padding: 10px 20px;
      border-bottom: 1px solid #ddd;
      display: flex;
      border-radius: 5px 5px 0 0;
      justify-content: space-between;

      .recycleList-item-header-right {
        color: #0966b4;
        cursor: pointer;
      }
    }
  }

  .recycleList-item-content {
    padding: 10px 20px;
    display: flex;
    justify-content: space-between;

    .recycleList-item-content-right {
      color: #0966b4;
      cursor: pointer;
    }
  }
}

.textBookTemplate-content {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 20px;

  .textBookTemplate-content-item {
    position: relative;
    background-color: #f6f9fa;
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    padding: 10px 15px;
    border-radius: 10px;
    border: 2px solid #ddd;
    position: relative;

    .active {
      position: absolute;
      top: 0;
      right: 0;
      width: 0;
      height: 0;

      border-style: solid;
      border-width: 0 0 50px 50px;
      border-radius: 0 0 10px 0;
      border-color: transparent transparent #67c239 transparent;
      transform: rotate(270deg);
      z-index: 9;

      &::after {
        content: "";
        position: absolute;
        width: 20px;
        height: 20px;

        transform: rotate(90deg);
        top: 25px;
        right: 5px;
        background-image: url("@/assets/images/rightIcon.svg");
        background-size: cover;
      }
    }

    .textBookTemplate-content-item-title {
      text-align: center;
      font-weight: bold;
      font-size: 16px;
    }

    .textBookTemplate-content-item-img {
      position: relative;

      &:hover {
        .textBookTemplate-content-item-img-opt-mask {
          opacity: 1;
          // transition: width 0.5s ease-in-out, background-color 1s linear;
        }
      }

      .textBookTemplate-content-item-img-opt-mask {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        opacity: 0;
        transition: opacity 0.3s ease-out;
        will-change: opacity;

        .textBookTemplate-content-item-img-opt-mask-bnt {
          color: #fff;
          cursor: pointer;
          margin: 0 10px;
          width: 56px;
          height: 56px;
          border-radius: 50%;
          display: flex;
          justify-content: center;
          align-items: center;
          background: linear-gradient(135deg, #0966b4 0%, #00b4db 100%);
          font-size: 24px;
          box-shadow: 0 4px 12px rgba(9, 102, 180, 0.3);
          transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);

          &:hover {
            transform: scale(1.15) rotate(5deg);
            box-shadow: 0 6px 16px rgba(9, 102, 180, 0.4);
            background: linear-gradient(135deg, #00b4db 0%, #0966b4 100%);
          }

          &:active {
            transform: scale(0.95);
          }
        }
      }

      .textBookTemplate-content-item-img-check {
        position: absolute;
        width: 20px;
        height: 20px;
        top: 10px;
        right: 10px;
        border-radius: 50%;
        background-color: #fff;
        border: 1px solid #ccc;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
      }
    }

    .isUse {
      background-color: #68c2ff54;
    }

    .isUse:hover {
      cursor: pointer;
      background-color: #0099ff8e;
    }
  }
}

:deep(.pagination-container) {

  position: relative !important;

}
</style>
