<template>
  <Cards class="top20">
    <el-table
      v-loading="loading"
      :data="bookList"
      :header-cell-style="{
        background: '#EDF4FD !important',
        color: '#666666',
        textAlign: 'center',
      }"
      :tree-props="{ checkStrictly: true }"
      row-key="bookId"
      @selection-change="handleSelectionChange"
      :cell-style="{ textAlign: 'center' }"
    >
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column
        label="教材编号"
        align="center"
        prop="bookNo"
        width="220"
        fixed="left"
      >
        <template #default="props">
          <span v-if="props.row.currentVersionId == props.row.lastVersionId">{{
            props.row.bookNo
          }}</span>
          <div v-if="props.row.currentVersionId != props.row.lastVersionId">
            <el-tag> 修正版本 </el-tag>{{ props.row.bookNo }}
          </div>
        </template>
      </el-table-column>

      <el-table-column
        label="教材名称"
        align="center"
        prop="bookName"
        width="150"
        show-overflow-tooltip
        fixed="left"
      />
      <el-table-column label="封面" align="center" prop="cover" width="100">
        <template #default="scope">
          <imagePreview :src="scope.row.cover || 'http://dutp-test.oss-cn-beijing.aliyuncs.com/1741232140222.png' " width="60px" height="80px" />
        </template>
      </el-table-column>
      <el-table-column
        label="ISBN/ISSN"
        v-if="activeName == 'publicBook'"
        align="center"
      width="240"
      >
      <template #default="scope">
        <div v-if="scope.row.isbn" style="font-weight: bold;">ISBN:{{ scope.row.isbn }}</div>
        <div v-else-if="scope.row.issn" style="font-weight: bold;">ISSN:{{ scope.row.issn }}</div>
        <div v-else>-</div>
      </template>
      </el-table-column>
    
      <!-- <el-table-column
        label="ISSN"
        v-if="activeName == 'publicBook'"
        align="center"
        prop="issn"
      /> -->
      <el-table-column
        label="教材状态"
        align="center"
        prop="publishStatus"
        width="80"
      >
        <template #default="scope">
          <div v-if="scope.row.currentVersionId != scope.row.lastVersionId">
            <el-tag> 未出版 </el-tag>
          </div>
          <el-tag v-else>{{
            getOptionDesc(bookPublishStatusList, scope.row.publishStatus)
          }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="节点" align="center" prop="stepName" width="120">
        <template #default="scope">
          <div v-if="scope.row.isAmending == true">
              <el-tag type="success">已发版</el-tag>
            </div>
            <el-tag v-else-if="scope.row.auditState == null || scope.row.auditState == 3" type="success">制作中</el-tag>
            <el-tag v-else type="success">{{ scope.row.stepName }}</el-tag>

        </template>
      </el-table-column>
      <el-table-column
        label="销售状态"
        align="center"
        v-if="activeName == 'publicBook'"
        prop="shelfState"
        width="100"
      >
        <template #default="scope">
          <div v-if="scope.row.currentVersionId != scope.row.lastVersionId">
            <el-tag  type="warning"> 未上架 </el-tag>
          </div>
          <el-tag v-else  type="warning">{{
             getOptionDesc(bookShelfStatusOptions, scope.row.shelfState)
          }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="主/副教材" align="center" prop="masterFlag">
        <template #default="scope">
          <el-tag type="info">{{
            getOptionDesc(bookNatureTypeList, scope.row.masterFlag)
          }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="关联教材"
        align="center"
        prop="deputyBookName"
        width="100"
        show-overflow-tooltip
      />
      <el-table-column
        width="100"
        label="定价（元）"
        align="center"
        v-if="activeName == 'publicBook'"
        prop="priceCounter"
      />
      <el-table-column
        label="售价（元）"
        width="100"
        align="center"
        v-if="activeName == 'publicBook'"
        prop="priceSale"
      >
      <template #default="scope">
          {{ scope.row.priceSale || (scope.row.masterFlag == 3 ? '0' : '')}}
        </template>
    </el-table-column>
      <el-table-column label="出版单位" align="center" prop="houseName" />
      <el-table-column
        label="学校"
        align="center"
        width="100"
        show-overflow-tooltip
        prop="schoolName"
      />
      <el-table-column
        label="出版时间"
        align="center"
        prop="publishDate"
        width="100"
      />
      <el-table-column label="版本号" align="center" prop="versionNo" />
      <el-table-column label="选题号" align="center" prop="topicNo" />
      <el-table-column label="版次" align="center" prop="edition" />

      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        width="100"
      />
      <el-table-column
        label="全书完成度"
        align="center"
        width="100"
        prop="completeRate"
      >
        <template #default="scope">
          {{ scope.row.completeRate + "%" }}
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" width="250" fixed="right">
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="View"
            v-if="scope.row.currentVersionId == scope.row.lastVersionId"
            @click="handleView(scope.row.bookId)"
            v-hasPermi="['book:book:view']"
          >
            查看
          </el-button>
          <el-button
            link
            type="primary"
            icon="RefreshLeft"
            v-if="scope.row.currentVersionId == scope.row.lastVersionId && scope.row.publishStatus == 2 && scope.row.shelfState != 3 && scope.row.isAmending == false"
            @click="handleAmend(scope.row)"
            v-hasPermi="['book:book:amend']"
          >
            修正
          </el-button>
          <el-button
            link
            type="primary"
            icon="CopyDocument"
            v-if="scope.row.currentVersionId == scope.row.lastVersionId"
            @click="handleCopy(scope.row)"
            v-hasPermi="['book:book:copy']"
          >
            复制
          </el-button>
          <el-button
            link
            type="primary"
            icon="Edit"
            v-if="scope.row.publishStatus == 1 && (scope.row.currentStepId == 1 || scope.row.auditState == null || scope.row.auditState == 3)"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['book:book:edit']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="primary"
            icon="Delete"
            v-if="scope.row.publishStatus == 1 && (scope.row.currentStepId == 1 || scope.row.auditState == null || scope.row.auditState == 3)"
            @click="handleDelete(scope.row)"
            v-hasPermi="['book:book:remove']"
            >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="page.pageNum"
      v-model:limit="page.pageSize"
      @pagination="getList(t_queryParams)"
    />
  </Cards>
</template>

<script setup name="ListTable">
import { listBook } from "@/api/book/book.js";

import {
  bookNatureTypeList,
  bookShelfStatusOptions,
  getOptionDesc,
} from "@/utils/optionUtil.js";

const { bookPublishStatusList } = defineProps({
  bookPublishStatusList: {
    type: Array,
    default: [],
  },
  activeName: {
    type: String,
    default: "publicBook",
  },
});
const emit = defineEmits([
  "delete",
  "amend",
  "edit",
  "copy",
  "view",
  "selectionChange",
]);

const { proxy } = getCurrentInstance();

const bookList = ref([]);
const loading = ref(true);
const total = ref(0);
const page = ref({
  pageNum: 1,
  pageSize: 10,
});
const t_queryParams = ref({});
//#region 获取数据相关

// 获取教材列表
async function getList(queryParams) {
  t_queryParams.value = queryParams;
  loading.value = true;
  let response = await listBook({
    ...queryParams,
    ...page.value,
  });
  bookList.value = response.rows;
  bookList.value.forEach((e) => {
    e.isAmending = false;
    if (e.masterBookName) {
      e.deputyBookName = e.masterBookName;
    }
    if (e.children) {
      e.isAmending = true;
      e.children = [
        {
          ...e.children[0],
          bookId: e.children[0].bookId + "amend",
        },
      ];
    }
  });
  total.value = response.total;
  loading.value = false;
}

//#endregion

//#region 操作相关

// 重置page
function resetPage() {
  page.value.pageNum = 1;
}

// 表格多选框选中数据
function handleSelectionChange(selection) {
  emit("selectionChange", selection);
}

// 修改按钮操作
function handleUpdate(row) {
  emit("edit", row);
}

// 查看按钮操作
function handleView(bookId) {
  proxy.$router.push({
    path: "/book/bookDetail",
    query: { bookId: bookId.replace("amend", "") },
  });
}

// 修正按钮操作
function handleAmend(row) {
  emit("amend", row);
}

// 复制按钮操作
function handleCopy(row) {
  emit("copy", row);
}

// 删除按钮操作
function handleDelete(row) {
  emit("delete", row);
}
//#endregion

//#endregion 暴露事件

defineExpose({
  getList,
  resetPage,
});
//#endregion
</script>
<style lang="scss" scoped>
.tool {
  padding: 20px 0 0;
}

.top20 {
  margin-top: 20px;
}

.pagination {
  float: right;
  margin: 20px 0;
}
</style>
