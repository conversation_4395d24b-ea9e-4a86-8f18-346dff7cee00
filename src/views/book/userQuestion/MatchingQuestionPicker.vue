<template>
    <div class="matching-picker">
      <div class="matching-columns">
        <!-- 左侧选项编辑区 -->
        <div class="column left-column">
          <div class="column-header">
            <h4>左侧选项</h4>
            <el-button v-if="!readonly" type="primary" link @click="addOption('left')">
              <el-icon><Plus /></el-icon>添加选项
            </el-button>
          </div>
          <div class="options-list">
            <div v-for="(option, index) in leftOptions" 
                 :key="'left-' + index"
                 :data-id="'left-' + option.id"
                 class="option-item">
              <div class="option-content">
                <editor v-model="option.content" :min-height="80" :readonly="readonly"/>
              </div>
              <div v-if="!readonly" class="option-actions">
                <el-button type="danger" link @click="removeOption('left', index)">
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
              <div v-if="!readonly" class="connection-point right">
                <el-button type="primary" circle size="small" @click="handleSelect('left', index)">
                  <el-icon><Connection /></el-icon>
                </el-button>
              </div>
            </div>
          </div>
        </div>
  
        <!-- 右侧选项编辑区 -->
        <div class="column right-column">
          <div class="column-header">
            <h4>右侧选项</h4>
            <el-button v-if="!readonly" type="primary" link @click="addOption('right')">
              <el-icon><Plus /></el-icon>添加选项
            </el-button>
          </div>
          <div class="options-list">
            <div v-for="(option, index) in rightOptions" 
                 :key="'right-' + index"
                 :data-id="'right-' + option.id"
                 class="option-item">
              <div v-if="!readonly" class="connection-point left">
                <el-button type="primary" circle size="small" @click="handleSelect('right', index)">
                  <el-icon><Connection /></el-icon>
                </el-button>
              </div>
              <div class="option-content">
                <editor v-model="option.content" :min-height="80" :readonly="readonly"/>
              </div>
              <div v-if="!readonly" class="option-actions">
                <el-button type="danger" link @click="removeOption('right', index)">
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
  
      <!-- 连线展示区域 -->
      <svg class="matching-lines" ref="svgContainer">
        <g v-for="(line, index) in matchingLines" 
           :key="index"
           @click="removeLine(index)"
           class="line-group"
           :class="{ 'readonly': readonly }">
          <line 
            :x1="line.x1" 
            :y1="line.y1" 
            :x2="line.x2" 
            :y2="line.y2"
            stroke="#409EFF" 
            stroke-width="2"/>
          <line 
            :x1="line.x1" 
            :y1="line.y1" 
            :x2="line.x2" 
            :y2="line.y2"
            stroke="transparent" 
            stroke-width="10"
            class="line-hitbox"/>
        </g>
      </svg>

      <!-- 添加蒙层，在只读模式下显示 -->
      <div v-if="readonly" class="picker-overlay"></div>
    </div>
  </template>
  
  <script setup>
  import { ref, reactive, onMounted, watch, nextTick } from 'vue';
  import Editor from "@/components/Editor/index.vue";
  
  const props = defineProps({
    modelValue: {
      type: Object,
      default: () => ({
        leftOptions: [],
        rightOptions: [],
        matchingPairs: []
      })
    },
    readonly: {
      type: Boolean,
      default: false
    }
  });
  
  const emit = defineEmits(['update:modelValue']);
  
  // 初始化数据
  const leftOptions = ref(props.modelValue.leftOptions.length ? 
    props.modelValue.leftOptions : 
    [{ content: '', selected: false, id: 0 }]
  );
  
  const rightOptions = ref(props.modelValue.rightOptions.length ? 
    props.modelValue.rightOptions : 
    [{ content: '', selected: false, id: 0 }]
  );
  
  const matchingLines = ref([]);
  const selectedLeft = ref(null);
  const selectedRight = ref(null);
  
  // 添加本地响应式数据
  const matchingPairs = ref(props.modelValue.matchingPairs || []);
  
  // 添加选项
  const addOption = (side) => {
    if (props.readonly) return; // 只读模式下不允许添加
    
    const options = side === 'left' ? leftOptions : rightOptions;
    const newId = Math.max(...options.value.map(opt => opt.id), -1) + 1;
    options.value.push({
      content: '',
      selected: false,
      id: newId
    });
    updateModelValue();
  };
  
  // 删除选项
  const removeOption = (side, index) => {
    if (props.readonly) return; // 只读模式下不允许删除
    
    const options = side === 'left' ? leftOptions : rightOptions;
    const removedId = options.value[index].id;
    options.value.splice(index, 1);
    
    // 删除相关的连线
    matchingPairs.value = matchingPairs.value.filter(pair => {
      if (side === 'left') {
        return pair.leftId !== removedId;
      } else {
        return pair.rightId !== removedId;
      }
    });
    
    updateModelValue();
    updateLines();
  };
  
  // 处理选项选中
  const handleSelect = (side, index) => {
    if (props.readonly) return; // 只读模式下不允许选择
    
    const option = side === 'left' ? leftOptions.value[index] : rightOptions.value[index];
    
    if (side === 'left') {
      // 取消其他左侧选中
      leftOptions.value.forEach((opt, i) => {
        if (i !== index) opt.selected = false;
      });
      option.selected = !option.selected;
      selectedLeft.value = option.selected ? option : null;
    } else {
      // 取消其他右侧选中
      rightOptions.value.forEach((opt, i) => {
        if (i !== index) opt.selected = false;
      });
      option.selected = !option.selected;
      selectedRight.value = option.selected ? option : null;
    }
  
    // 如果两边都选中了，创建连线
    if (selectedLeft.value && selectedRight.value) {
      createMatchingPair();
    }
  };
  
  // 创建匹配对
  const createMatchingPair = () => {
    if (props.readonly) return; // 只读模式下不允许创建匹配
    
    // 确保两边都有选中的选项
    if (!selectedLeft.value || !selectedRight.value) {
      return;
    }

    // 检查是否已存在相同的配对
    const existingPair = matchingPairs.value.find(
      pair => pair.left.id === selectedLeft.value.id && 
             pair.right.id === selectedRight.value.id
    );

    if (!existingPair) {
      const newPair = {
        left: {
          id: selectedLeft.value.id,
          content: selectedLeft.value.content
        },
        right: {
          id: selectedRight.value.id,
          content: selectedRight.value.content
        }
      };
      
      matchingPairs.value.push(newPair);
      updateModelValue();
      updateLines();
    }

    // 重置选中状态
    if (selectedLeft.value) {
      selectedLeft.value.selected = false;
    }
    if (selectedRight.value) {
      selectedRight.value.selected = false;
    }
    selectedLeft.value = null;
    selectedRight.value = null;
  };
  
  // 更新连线
  const updateLines = () => {
    nextTick(() => {
      matchingLines.value = matchingPairs.value.map(pair => {
        const leftEl = document.querySelector(`[data-id="left-${pair.left.id}"]`);
        const rightEl = document.querySelector(`[data-id="right-${pair.right.id}"]`);
        
        if (!leftEl || !rightEl) return null;
  
        const leftRect = leftEl.getBoundingClientRect();
        const rightRect = rightEl.getBoundingClientRect();
        const containerRect = document.querySelector('.matching-picker').getBoundingClientRect();
  
        return {
          x1: leftRect.right - containerRect.left,
          y1: leftRect.top - containerRect.top + leftRect.height / 2,
          x2: rightRect.left - containerRect.left,
          y2: rightRect.top - containerRect.top + rightRect.height / 2
        };
      }).filter(line => line !== null);
    });
  };
  
  // 更新父组件的值
  const updateModelValue = () => {
    if (props.readonly) return; // 只读模式下不更新父组件值
    
    emit('update:modelValue', {
      leftOptions: leftOptions.value,
      rightOptions: rightOptions.value,
      matchingPairs: matchingPairs.value
    });
  };
  
  // 监听选项变化，更新连线
  watch([leftOptions, rightOptions], () => {
    nextTick(() => {
      updateLines();
    });
  });
  
  // 组件挂载后初始化连线
  onMounted(() => {
    const resizeObserver = new ResizeObserver(() => {
      updateLines();
    });
    
    const container = document.querySelector('.matching-picker');
    if (container) {
      resizeObserver.observe(container);
    }
    
    updateLines();
  });

  // 删除连线
  const removeLine = (index) => {
    if (props.readonly) return; // 只读模式下不允许删除连线
    
    // 删除匹配对
    matchingPairs.value.splice(index, 1);
    updateModelValue();
    updateLines();
  };
  </script>
  
  <style lang="scss" scoped>
  .matching-picker {
    position: relative;
    padding: 20px;
    
    .matching-columns {
      display: flex;
      justify-content: space-between;
      gap: 20px;
      
      .column {
        &.left-column,
        &.right-column {
          width: 40%;
        }

        &.middle-column {
          width: 20%;
          display: flex;
          justify-content: center;
          align-items: flex-start;
          padding-top: 50px;
        }
        
        .column-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 15px;
          
          h4 {
            margin: 0;
          }
        }
        
        .options-list {
          display: flex;
          flex-direction: column;
          gap: 15px;
        }
      }
    }
    
    .option-item {
      position: relative;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      padding: 10px;
      background: #fff;
      
      .option-content {
        margin-bottom: 10px;
        
        :deep(img) {
          max-width: 100%;
          height: auto;
          object-fit: contain;
        }
      }
      
      .option-actions {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: 8px;
      }

      .connection-point {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);

        &.right {
          right: -16px;
        }

        &.left {
          left: -16px;
        }

        .el-button {
          padding: 6px;
          &.is-selected {
            background-color: #409EFF;
            color: #fff;
          }
        }
      }
    }
    
    .matching-lines {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      
      .line-group {
        cursor: pointer;
        pointer-events: all;
        
        &:hover line:first-child {
          stroke: #F56C6C;
          stroke-dasharray: 5;
        }
        
        &.readonly {
          pointer-events: none;
          cursor: default;
        }
        
        .line-hitbox {
          pointer-events: all;
        }
      }
    }

    /* 添加蒙层样式 */
    .picker-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(255, 255, 255, 0.01);
      z-index: 10;
      cursor: not-allowed;
    }
  }
  </style>