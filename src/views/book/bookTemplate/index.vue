<template>
  <div class="app-container">
    <Cards>
      <div class="tool">
        <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="100px">
          <el-form-item prop="modal" label="模板名称">
            <el-input v-model="queryParams.modal" placeholder="请输入模板名称" clearable @keyup.enter="handleQuery" />
          </el-form-item>

          <el-form-item prop="serialNumber" label="序号">
            <el-input v-model="queryParams.serialNumber" placeholder="请输入序号" clearable @keyup.enter="handleQuery" />
          </el-form-item>

          <el-form-item prop="type" label="分类">
            <el-select v-model="queryParams.type" placeholder="全部" clearable style="width: 120px">
              <el-option v-for="item in bookTemplateTypeList" :key="item.typeId" :label="item.typeName"
                :value="item.typeId" />
            </el-select>
          </el-form-item>

          <el-form-item prop="themeColor" label="颜色">
            <el-select v-model="queryParams.themeColor" placeholder="全部" clearable style="width: 100px">
              <el-option v-for="item in bookTemplateThemeList" :key="item.value" :value="item.value" :label="item.label"
                style="display: flex;justify-content: center;align-items: center;">
                <div :style="`width: 20px;height: 20px;background-color: ${item.label};`" />
              </el-option>
              <template #label="{ label, value }">
                <div style="display: flex; align-items: center; justify-content: center">
                  <div :style="`width: 20px;height: 20px;background-color:${label};`" />
                </div>
              </template>
            </el-select>
          </el-form-item>

          <el-form-item prop="sortBy" label="排序方式">
            <el-select v-model="queryParams.sortBy" placeholder="默认" clearable style="width: 120px">
              <el-option v-for="item in bookTemplateSortList" :key="item.value" :label="item.label"
                :value="item.value" />

            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd"
              v-hasPermi="['book:template:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Position" @click="handleQueryDefault"
              v-hasPermi="['book:template:add']">搜索默认模板</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </div>
    </Cards>

    <Cards class="top20">
      <el-table v-loading="loading" :data="templateList" height="700" :header-cell-style="{
        background: '#EDF4FD !important',
        color: '#666666',
        textAlign: 'center',
      }" :cell-style="{ textAlign: 'center' }">
        <el-table-column label="模板名称" align="center" prop="modal">
          <template #default="{ row }">
            <div style="display: flex;padding:0 30px;">
              <div style="font-weight: bold; color:#333;padding-right:10px;">{{ row.serialNumber || '' }} </div>
              <div style="color:#999;">{{ row.modal }}</div>
            </div>

          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" prop="status">
          <template #default="scope">
            <el-switch v-model="scope.row.status" :active-value="1" :inactive-value="2"
              @change="handleStatusChange(scope.row)"></el-switch>
          </template>
        </el-table-column>
        <el-table-column label="排序" align="center" prop="sort" width="180">
          <template #default="scope">
            <el-input-number :min="1" :max="9999" v-model="scope.row.sort"
              @change="handleSort(scope.row)"></el-input-number>
          </template>
        </el-table-column>
        <el-table-column label="模板样式" align="center" prop="imgUrl" width="100">
          <template #default="scope">
            <imagePreview :src="scope.row.imgUrl" width="60px" height="80px" />
          </template>
        </el-table-column>
        <el-table-column label="默认" align="center" prop="isDefault">
          <template #default="scope">
            <el-tag type="success" v-if="scope.row.isDefault == 1">默认</el-tag>
            <el-tag v-if="scope.row.isDefault == 0">非默认</el-tag>
          </template>
        </el-table-column>

        <el-table-column label="发布时间" align="center" prop="createTime" width="180">
          <template #default="scope">
            {{ formatDateYmd(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="发布人" align="center" prop="nickName" width="100" />

        <el-table-column label="操作" align="center" width="250" fixed="right" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
              v-hasPermi="['book:template:edit']">修改</el-button>
            <el-button link type="primary" icon="Edit" v-if="scope.row.isDefault != 1 && scope.row.status == 1"
              @click="handleDefault(scope.row)" v-hasPermi="['book:template:isDefault']">设为默认</el-button>
            <el-button link type="primary" icon="Edit" @click="handleDowload(scope.row)"
              v-hasPermi="['book:template:edit']">下载</el-button>
            <el-button link type="primary" icon="PictureRounded"
              @click="handleShowIconCompent(scope.row)">图标组件</el-button>
            <el-button link type="primary" icon="Delete" v-if="scope.row.isDefault == 0"
              @click="handleDelete(scope.row)" v-hasPermi="['book:template:remove']">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </Cards>

    <!-- 添加或修改教材模板对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body class="custom-transition-dialog">
      <el-form ref="templateRef" :model="form" :rules="rules" :label-width="92">
        <div class="defalut-main">
          <div class="defalut-title">基本设置</div>
          <el-row>
            <el-col :span="10">
              <el-form-item label="模板名称" prop="modal">
                <el-input v-model="form.modal" placeholder="请输入模板名称" maxlength="25" />
              </el-form-item>

            </el-col>

            <el-col :span="8">
              <el-form-item label="分类" prop="type">
                <el-select v-model="form.type" placeholder="分类">
                  <el-option v-for="item in bookTemplateTypeList" :key="item.typeId" :label="item.typeName"
                    :value="item.typeId" />
                </el-select>
              </el-form-item>
           
            </el-col>
            <el-col :span="6">
              <el-form-item prop="themeColor" label="颜色">
                <el-select v-model="form.themeColor" placeholder="颜色" clearable style="width: 70px;">
                  <el-option v-for="item in bookTemplateThemeList" :key="item.value" :value="item.value"
                    :label="item.label" style="display: flex;justify-content: center;align-items: center;">
                    <div :style="`width: 20px;height: 20px;background-color: ${item.label};`" />
                  </el-option>
                  <template #label="{ label, value }">
                    <div style="display: flex; align-items: center; justify-content: center">
                      <div :style="`width: 20px;height: 20px;background-color:${label};`" />
                    </div>
                  </template>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="30">
            <el-col :span="12">
              <el-form-item label="模板背景图" prop="imgUrl">
                <ImgUpload :limit="1" v-model="form.imgUrl" />
              </el-form-item></el-col>
            
              
            
          </el-row>
        </div>

        <div class="defalut-main">
          <div class="defalut-title">章头模板设置</div>
          <el-row>
            <el-col :span="14">
              <el-form-item label="章头背景图" prop="chapterHeaderUrl">
                <ImgUpload :limit="1" v-model="form.chapterHeaderUrl" @change="handleChapterHeaderUrl" />
              </el-form-item></el-col>
            <el-col :span="10">
              <el-form-item label="章头高度" prop="chapterHeaderHeight">
                <el-input-number v-model="form.chapterHeaderHeight" :step="1" :min="0" :max="999999" step-strictly />
              </el-form-item>
              <el-form-item label="字体颜色" prop="chapterFontColor">
                <el-color-picker v-model="form.chapterFontColor" /> </el-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="defalut-main">
          <div class="defalut-title">节头模板设置</div>
          <el-row>
            <el-col :span="14">
              <el-form-item label="节头背景图" prop="jointHeaderUrl">
                <ImgUpload :limit="1" v-model="form.jointHeaderUrl" @change="handleJointHeaderUrl" />
              </el-form-item></el-col>
            <el-col :span="10">
              <el-form-item label="节头高度" prop="jointHeight">
                <el-input-number v-model="form.jointHeight" :step="1" :min="0" :max="999999" step-strictly />
              </el-form-item>
              <el-form-item label="字体颜色" prop="jointFontColor">
                <el-color-picker v-model="form.jointFontColor" /> </el-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="defalut-main">
          <div class="defalut-title">页设置</div>
          <el-row>
            <el-col :span="12">
              <el-form-item label="页码位置" prop="pagesPosition">
                <el-select v-model="form.pagesPosition" style="width: 200px;">
                  <el-option v-for="item in selectList" :key="item.value" :value="item.value"
                    :label="item.label"></el-option>
                </el-select>
              </el-form-item></el-col>
            <el-col :span="12">
              <el-form-item label="页码字体颜色" prop="pagesFontColor" :label-width="110">
                <el-color-picker v-model="form.pagesFontColor" /> </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="页眉背景设置" prop="headerUrl" :label-width="100">
                <ImgUpload :limit="1" v-model="form.headerUrl" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="页脚背景设置" prop="footerUrl" :label-width="110">
                <ImgUpload :limit="1" v-model="form.footerUrl" />
              </el-form-item>
            </el-col>

          </el-row>
        </div>

        <div class="defalut-main">
          <div class="defalut-title">资源组件模板设置</div>
          <el-row>
            <el-col :span="14">
              <el-form-item label="资源背景图" prop="orderTemplateBgUrl">
                <ImgUpload :limit="1" v-model="form.orderTemplateBgUrl" />
              </el-form-item>
            </el-col>
            <el-col :span="10" >
              <el-alert title="资源组件包括" description="折叠组件、视频组件、文件组件、实训组件、试卷、作业、心理测试、教学资源、虚拟仿真、拓展阅读" type="primary" show-icon />
              <el-form-item label="资源字体模式" prop="theme" :label-width="110">
              <el-radio-group v-model="form.theme">
                <el-radio value="light">浅色</el-radio>
                <el-radio value="dark">深色</el-radio>
              </el-radio-group>
            </el-form-item>
            </el-col>
          </el-row>
        </div>

        <div class="defalut-main">
          <div class="defalut-title">其他上传</div>
          <el-row style="margin-top:20px;">
            <el-col :span="24">
              <el-form-item label="素材" prop="materialUrl" :label-width="60">
              <FileUpload v-model="form.materialUrl" :fileSize="20" :limit="1" :fileType="['zip']" />
            </el-form-item>
             </el-col>
          </el-row>
        </div>
      </el-form>


      <!-- <el-form ref="templateRef" :model="form" :rules="rules" label-width="120px">
       
        <el-row>
          <el-col :span="24">
            <el-form-item label="模板名称" prop="modal">
              <el-input v-model="form.modal" placeholder="请输入模板名称" maxlength="25"  style="width: 300px;"/>
            </el-form-item>
          </el-col>
       
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="分类" prop="type">
              <el-select v-model="form.type" placeholder="分类">
                  <el-option v-for="item in bookTemplateTypeList" :key="item.typeId" :label="item.typeName"
                  :value="item.typeId" />
              </el-select>
            </el-form-item></el-col>
          <el-col :span="12">
            <el-form-item prop="themeColor" label="颜色">
              <el-select v-model="form.themeColor" placeholder="颜色" clearable>
                <el-option v-for="item in bookTemplateThemeList" :key="item.value" :value="item.value"
                  :label="item.label" style="display: flex;justify-content: center;align-items: center;">
                  <div :style="`width: 20px;height: 20px;background-color: ${item.label};`" />
                </el-option>
                <template #label="{ label, value }">
                  <div style="display: flex; align-items: center; justify-content: center">
                    <div :style="`width: 20px;height: 20px;background-color:${label};`" />
                  </div>
                </template>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="模板背景图" prop="imgUrl">
              <ImgUpload :limit="1" v-model="form.imgUrl" />
            </el-form-item></el-col>
          <el-col :span="12">
            <el-form-item label="资源背景图" prop="orderTemplateBgUrl">
              <ImgUpload :limit="1" v-model="form.orderTemplateBgUrl" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
        
          <el-col :span="12">
            <el-form-item label="页眉" prop="headerUrl">
              <ImgUpload :limit="1" v-model="form.headerUrl" />
            </el-form-item></el-col>

          <el-col :span="12">
            <el-form-item label="页脚" prop="footerUrl">
              <ImgUpload :limit="1" v-model="form.footerUrl" />
            </el-form-item></el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="章头背景图" prop="chapterHeaderUrl">
              <ImgUpload :limit="1" v-model="form.chapterHeaderUrl" @change="handleChapterHeaderUrl" />
            </el-form-item></el-col>
          <el-col :span="12">
            <el-form-item label="节头背景图" prop="jointHeaderUrl">
              <ImgUpload :limit="1" v-model="form.jointHeaderUrl" @change="handleJointHeaderUrl" />
            </el-form-item></el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="章头字体颜色" prop="chapterFontColor">
              <el-color-picker v-model="form.chapterFontColor" /> </el-form-item></el-col>
          <el-col :span="12">
            <el-form-item label="节头字体颜色" prop="jointFontColor">
              <el-color-picker v-model="form.jointFontColor" /> </el-form-item></el-col>
        </el-row>

        <el-row>
      
          <el-col :span="12">
            <el-form-item label="页码字体颜色" prop="pagesFontColor">
              <el-color-picker v-model="form.pagesFontColor" /> </el-form-item></el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="章头高度" prop="chapterHeaderHeight">
              <el-input-number v-model="form.chapterHeaderHeight" :step="1" :min="0" :max="999999" step-strictly />
            </el-form-item></el-col>
          <el-col :span="12">
            <el-form-item label="节头高度" prop="jointHeight">
              <el-input-number v-model="form.jointHeight" :step="1" :min="0" :max="999999" step-strictly />
            </el-form-item></el-col>
        </el-row>
        <el-row>
        
          <el-col :span="12">
            <el-form-item label="页码位置" prop="pagesPosition">
              <el-select v-model="form.pagesPosition">
                <el-option v-for="item in selectList" :key="item.value" :value="item.value" :label="item.label" ></el-option>
              </el-select>
            </el-form-item></el-col>
            <el-col :span="12">
            <el-form-item label="主题" prop="theme">
              <el-radio-group v-model="form.theme">
                <el-radio value="light">light</el-radio>
                <el-radio value="dark">dark</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
      
        </el-row>

       
        <el-row>
          <el-col :span="24">
           
          </el-col>
        </el-row>
      </el-form> -->

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 编辑 -->
    <el-dialog title="图标组件" v-model="previewOpen" width="800px" append-to-body>
      <div class="dialog-content">
        <div class="dialog-add">
          <el-button type="primary" @click="addTemplateImage">添加</el-button>
        </div>
        <el-tabs v-model="activeName" class="demo-tabs" @tab-click="exchangeTab">
          <el-tab-pane label="背景边框" name="first">
            <el-table :data="bookTemplateImageList" border style="width: 100%">
              <el-table-column prop="url" label="图片" width="430">
                <template #default="scope">
                  <imagePreview :src="scope.row.url" width="400px" height="60px" />
                </template>
              </el-table-column>
              <el-table-column label="创建时间" align="center" prop="createTime" width="180" />
              <el-table-column fixed="right" label="操作">
                <template #default="{ row }">
                  <el-button link type="primary" size="small" @click="updateImage(row)">修改</el-button>
                  <el-button link type="primary" size="small" @click="deleteImage(row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="图标" name="second">
            <el-table :data="bookTemplateImageList" border style="width: 100%" :cell-style="{ textAlign: 'center' }">
              <el-table-column prop="url" label="图片" width="300">
                <template #default="scope">
                  <imagePreview :src="scope.row.url" width="60px" height="60px" />
                </template>
              </el-table-column>
              <el-table-column label="创建时间" align="center" prop="createTime" width="280" />
              <el-table-column fixed="right" label="操作">
                <template #default="{ row }">
                  <el-button link type="primary" size="small" @click="updateImage(row)">修改</el-button>
                  <el-button link type="primary" size="small" @click="deleteImage(row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-dialog>

  </div>
</template>

<script setup name="bookTemplate">
import {
  listTemplate,
  getTemplate,
  delTemplate,
  addTemplate,
  updateTemplate,
  editDefault,
  editIsEnable,
} from "@/api/book/bookTemplate";
import {
  listBookTemplateImage,
  updateBookTemplateImage,
  addBookTemplateImage,
  delBookTemplateImage,
} from "@/api/book/bookTemplateImage";
import ImgUpload from "@/components/ImageUpload/ImgUpload.vue";
import { chooseFile } from "@/utils/file";
import { formatDateYmd } from "@/utils/index.js";
import { listBookTemplateTypeNoPage } from "@/api/book/bookTemplateType.js";
import { bookTemplateSortList, bookTemplateThemeList } from '@/utils/optionUtil'
const { proxy } = getCurrentInstance();

const templateList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const single = ref(true);
const total = ref(0);
const title = ref("");
const previewOpen = ref(false);
const activeName = ref("first");
const bookTemplateImageList = ref([]);


const selectList = [
  { label: "左", value: "left" },
  { label: "右", value: "right" },
  { label: "中", value: "center" },
]
const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    templateName: null,
    cover: null,
    templateTypeId: null,
    specialityId: null,
    mainColor: null,
    firstLevelTitle: null,
    secondLevelTitle: null,
    thirdLevelTitle: null,
    forthLevelTitle: null,
    fifthLevelTitle: null,
    sixthLevelTitle: null,
    seventhLevelTitle: null,
    mainText: null,
    chapterHeader: null,
    sectionHeader: null,
    tableFill: null,
    resourceFill: null,
    status: null,
    sort: null,
  },
  imageQueryParams: {
    type: 1,
    belongTo: 1,
    templateId: null,
  },
  rules: {
    modal: [{ required: true, message: "模板名称不能为空", trigger: "blur" }],
    imgUrl: [
      { required: true, message: "模板背景图不能为空", trigger: "blur" },
    ],

    // headerUrl: [
    //   { required: true, message: "页眉背景图不能为空", trigger: "blur" },
    // ],
    // contentUrl: [
    //   { required: true, message: "内容背景图不能为空", trigger: "blur" },
    // ],
    // footerUrl: [{ required: true, message: "页脚不能为空", trigger: "blur" }],
    type: [
      { required: true, message: "分类不能为空", trigger: "blur" },
    ],
    themeColor: [
      { required: true, message: "颜色不能为空", trigger: "blur" },
    ],
    chapterHeaderUrl: [
      { required: true, message: "章头背景图不能为空", trigger: "blur" },
    ],
    jointHeaderUrl: [
      { required: true, message: "节头背景图不能为空", trigger: "blur" },
    ],
    chapterFontColor: [
      { required: true, message: "章头字体颜色不能为空", trigger: "blur" },
    ],
    jointFontColor: [
      { required: true, message: "节头字体颜色不能为空", trigger: "blur" },
    ],
    chapterHeaderHeight: [
      { required: true, message: "章头高度不能为空", trigger: "blur" },
    ],
    jointHeight: [
      { required: true, message: "节头高度不能为空", trigger: "blur" },
    ],
    // orderTemplateColor: [
    //   { required: true, message: "资源字体颜色不能为空", trigger: "blur" },
    // ],

    pagesFontColor: [
      { required: true, message: "页码字体颜色不能为空", trigger: "blur" },
    ],
    pagesAlign: [
      { required: true, message: "页码对齐方式不能为空", trigger: "blur" },
    ],
    pagesPosition: [
      { required: true, message: "页码位置不能为空", trigger: "blur" },
    ],
    orderTemplateMarginLeft: [
      {
        required: true,
        message: "资源控件名称与标题的间距不能为空",
        trigger: "blur",
      },
    ],
    orderTemplateBgUrl: [
      { required: true, message: "资源背景图不能为空", trigger: "blur" },
    ],
    theme: [{ required: true, message: "主题不能为空", trigger: "blur" }],
  },
});
const bookTemplateTypeList = ref([])
const { queryParams, form, rules, imageQueryParams } = toRefs(data);

// 查询教材模板列表
function getList() {
  loading.value = true;
  listTemplate(queryParams.value).then((response) => {
    templateList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 教材模板分类列表
function getBookTemplateTypeList() {
  listBookTemplateTypeNoPage().then((response) => {
    bookTemplateTypeList.value = response.data
  })
}

// 获取模板图标库
async function getTemplateImageList() {
  bookTemplateImageList.value = [];
  const res = await listBookTemplateImage(imageQueryParams.value);
  bookTemplateImageList.value = res.data;
}

// 搜索默认
function handleQueryDefault() {
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    isDefault: 1,
  }
  getList();
}

function getImageSizeByUrl(url) {
  return new Promise(function (resolve, reject) {
    let image = new Image();
    image.onload = function () {
      resolve({
        width: image.width,
        height: image.height
      });
    };
    image.onerror = function () {
      reject(new Error('error'));
    };
    image.src = url;
  });
}

async function handleChapterHeaderUrl() {
  if (form.value.chapterHeaderUrl) {
    const res = await getImageSizeByUrl(form.value.chapterHeaderUrl)
    form.value.chapterHeaderHeight = res.height;
  }

}
async function handleJointHeaderUrl() {
  if (form.value.jointHeaderUrl) {
    const res = await getImageSizeByUrl(form.value.jointHeaderUrl)
    form.value.jointHeight = res.height;
  }
}

function handleDowload(row) {
  if (!row.materialUrl) {
    return proxy.$modal.msgError("素材包不存在");
  }
  const link = document.createElement('a');
  link.href = row.materialUrl;
  link.download = row.modal + "-素材.zip";
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);

}

// 设为默认
function handleDefault(row) {
  editDefault({
    templateId: row.templateId,
  }).then((res) => {
    proxy.$modal.msgSuccess("默认修改成功");
    getList();
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 打开图标组件
async function handleShowIconCompent(item) {
  imageQueryParams.value.templateId = item.templateId;
  await getTemplateImageList();
  previewOpen.value = true;
}

// 切换tab
function exchangeTab(tab, event) {
  console.log(tab, event);
  if (activeName.value != tab.paneName) {
    activeName.value = tab.paneName;
    imageQueryParams.value.belongTo = tab.paneName == "first" ? 1 : 2;
    getTemplateImageList();
  }
}

// 上传模板背景图
function addTemplateImage() {
  chooseFile(proxy, (file) => {
    addBookTemplateImage({
      type: 1,
      belongTo: imageQueryParams.value.belongTo,
      templateId: imageQueryParams.value.templateId,
      url: file.fileUrl,
    }).then((res) => {
      proxy.$modal.msgSuccess("上传成功");
      getTemplateImageList();
    });
  });
}
// 更新图片
function updateImage(item) {
  chooseFile(proxy, (file) => {
    updateBookTemplateImage({
      imageId: item.imageId,
      url: file.fileUrl,
    }).then((res) => {
      proxy.$modal.msgSuccess("上传成功");
      getTemplateImageList();
    });
  });
}
// 删除图片
function deleteImage(item) {
  proxy.$modal
    .confirm("是否确认删除该模板图片?")
    .then(function () {
      return delBookTemplateImage(item.imageId);
    })
    .then(() => {
      getTemplateImageList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch((err) => {
      console.error(err);
    });
}
// 表单重置
function reset() {
  form.value = {
    templateId: null,
    templateName: null,
    cover: null,
    templateTypeId: null,
    specialityId: null,
    mainColor: null,
    firstLevelTitle: null,
    secondLevelTitle: null,
    thirdLevelTitle: null,
    forthLevelTitle: null,
    fifthLevelTitle: null,
    sixthLevelTitle: null,
    seventhLevelTitle: null,
    mainText: null,
    chapterHeader: null,
    sectionHeader: null,
    tableFill: null,
    resourceFill: null,
    status: null,
    sort: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    delFlag: null,
    chapterFontColor: '#666666',
    jointFontColor: '#666666',
    orderTemplateColor: '#666666',
    pagesFontColor: '#666666',

  };
  proxy.resetForm("templateRef");
}

// 模板状态修改
function handleStatusChange(row) {
  if (row.isDefault == 1) {
    proxy.$modal.msgError("默认模板不能修改状态");
    row.status = row.status == 1 ? 2 : 1;
    return;
  }
  let text = row.status == 1 ? "启用" : "停用";
  proxy.$modal
    .confirm('确认要' + text + '"' + row.modal + '"模板吗?')
    .then(function () {
      return editIsEnable({
        templateId: row.templateId,
        status: row.status,
      });
    })
    .then(() => {
      proxy.$modal.msgSuccess(text + "成功");
    })
    .catch(function () {
      row.status = row.status == 1 ? 2 : 1;
    });
}

// 排序
function handleSort(row) {
  if (row.sort < 0) {
    proxy.$modal.msgError("顺序不能小于0");
    getList();
    return;
  }

  updateTemplate({
    templateId: row.templateId,
    sort: row.sort,
  }).then((response) => {
    proxy.$modal.msgSuccess("修改成功");
    getList();
    open.value = false;
  });
}

// 搜索按钮操作
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

// 重置按钮操作
function resetQuery() {
  proxy.resetForm("queryRef");
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
  }
  handleQuery();
}

// 新增按钮操作
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加教材模板";
}

// 修改按钮操作
function handleUpdate(row) {
  reset();
  const _templateId = row.templateId;
  getTemplate(_templateId).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "修改教材模板";
  });
}

// 提交按钮
function submitForm() {
  proxy.$refs["templateRef"].validate((valid) => {
    if (valid) {
      if (form.value.templateId != null) {
        updateTemplate(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addTemplate(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

// 删除按钮操作
function handleDelete(row) {
  if (row.isDefault == 1) {
    proxy.$modal.msgError("默认模板不能删除");
    return;
  }
  const _templateIds = row.templateId;
  proxy.$modal
    .confirm('是否确认删除教材模板为"' + row.modal + '"的数据项？')
    .then(function () {
      return delTemplate(_templateIds);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => { });
}

getBookTemplateTypeList()
getList();
</script>
<style lang="scss" scoped>
.tool {
  padding: 20px 0 0;
}

.top20 {
  margin-top: 20px;
}

.pagination {
  float: right;
  margin: 20px 0;
}

.dialog-content {
  position: relative;

  .dialog-add {
    position: absolute;
    right: 0;
    z-index: 9999;
  }


}

::v-deep(.avatar-uploader .el-upload) {
  border: 1px dashed #eee;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
}
</style>

<style lang="scss">
.custom-transition-dialog>.el-dialog__body>.el-form>.defalut-main {
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 20px;
  position: relative;
  width: 100%;
  margin-top: 20px;
}

.custom-transition-dialog>.el-dialog__body>.el-form>.defalut-main>.defalut-title {
  position: absolute;
  top: -10px;
  left: 20px;
  font-size: 16px;
  color: #333;
  font-weight: bold;
  background-color: #fff;

}
.custom-transition-dialog>.el-dialog__body>.el-form>.defalut-main > .el-row >.el-col > .el-alert {
  background-color: rgb(236, 245, 255);
  margin-bottom: 20px;
}


</style>
