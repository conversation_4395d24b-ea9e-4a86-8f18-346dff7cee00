<template>
  <div class="app-container">
    <Cards>
      <div class="tool">
        <el-form
          :model="queryParams"
          ref="queryRef"
          :inline="true"
          label-width="68px"
        >
          <el-form-item
            prop="statementNo"
            label="结算单号"
          >
            <el-input
              v-model="queryParams.statementNo"
              placeholder="请输入结算单号"
              style="width: 240px"
              clearable
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item
            prop="orderNo"
            label="订单号"
          >
            <el-input
              v-model="queryParams.orderNo"
              placeholder="请输入结算单号"
              style="width: 240px"
              clearable
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item
            prop="merchanName"
            label="书商名称"
          >
            <el-select
              v-model="queryParams.merchanName"
              placeholder="请选择书商名称"
              style="width: 240px"
            >
              <el-option
                v-for="item in merchanList"
                :key="item.merchantId"
                :label="item.merchanName"
                :value="item.merchanName"
              />
            </el-select>
          </el-form-item>

          <el-form-item
            prop="schoolName"
            label="学校名称"
          >
            <el-select
              v-model="queryParams.schoolName"
              placeholder="请选择学校名称"
              style="width: 240px"
            >
              <el-option
                v-for="item in schoolList"
                :key="item.schoolId"
                :label="item.schoolName"
                :value="item.schoolName"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="筛选时间">
            <el-date-picker
              v-model="queryParams.createTimeList"
              value-format="YYYY-MM-DD"
              type="daterange"
              range-separator="到"
              start-placeholder="请选择开始日期"
              end-placeholder="请选择结束日期"
              style="width: 240px"
            />
          </el-form-item>

          

          <el-form-item
            prop="statementStatus"
            label="状态"
          >
            <el-select
              v-model="queryParams.statementStatus"
              placeholder="请选择状态"
              style="width: 240px"
            >
              <el-option
                v-for="item in statementStatus"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item
            prop="invoiceStatus"
            label="开票状态"
          >
            <el-select
              v-model="queryParams.invoiceStatus"
              placeholder="请选择开票状态"
              style="width: 240px"
            >
              <el-option
                v-for="item in invoiceStatus"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            prop="areaName"
            label="地区名称"
          >
            <el-select
              v-model="queryParams.areaName"
              placeholder="请选择地区名称"
              style="width: 240px"
            >
              <el-option
                v-for="item in areaList"
                :key="item.areaId"
                :label="item.areaName"
                :value="item.areaName"
              />
            </el-select>
          </el-form-item>
         
          <el-form-item>
            <el-button
              type="primary"
              icon="Search"
              @click="handleQuery"
            >搜索</el-button>
            <el-button
              icon="Refresh"
              @click="resetQuery"
            >重置</el-button>
          </el-form-item>
        </el-form>

        <el-row
          :gutter="10"
          class="mb8"
        >
          <el-col :span="1.5">
            <el-button
              type="primary"
              plain
              @click="handleInvoice"
            >对账</el-button>
          </el-col>
        </el-row>
      </div>
    </Cards>

    <Cards class="top20">

      <el-table
        v-loading="loading"
        :data="statementList"
        @selection-change="handleSelectionChange"
        :header-cell-style="{ background: '#EDF4FD !important', color: '#666666', textAlign: 'center' }"
        :cell-style="{ textAlign: 'center' }"
      >
        <el-table-column
          label="结算单号"
          align="center"
          prop="statementNo"
          width="180"
        />
        <el-table-column
          label="订单号"
          align="center"
          prop="orderNo"
          width="180"
        />
        <el-table-column
          label="采购数量"
          align="center"
          prop="totalBookQuantity"
        />
        <el-table-column
          label="商品总额"
          align="center"
          prop="totalPrice"
        />
        <el-table-column
          label="应付金额"
          align="center"
          prop="totalPayAmount"
        />
        <el-table-column
          label="书商名称"
          align="center"
          prop="uniqueMerchanNames"
        />
        <el-table-column
          label="学校名称"
          align="center"
          prop="schoolName"
          show-overflow-tooltip
          width="200"
        />
        <el-table-column
          label="地区名称"
          align="center"
          prop="areaName"
        />
        <el-table-column
          label="创建人"
          align="center"
          prop="createBy"
        />
        <el-table-column
          label="创建时间"
          align="center"
          prop="createTime"
          width="200"
        />
        <el-table-column
          label="状态"
          align="center"
          prop="statementStatus"
        >
          <template #default="scope">
            <el-tag>{{ getOptionDesc(statementStatus,scope.row.statementStatus) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="开票状态"
          align="center"
          prop="invoiceStatus"
          width="100"
        >
          <template #default="scope">
            <el-tag>{{ getOptionDesc(invoiceStatus,scope.row.invoiceStatus) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          width="300"
          class-name="small-padding fixed-width"
        >
          <template #default="scope">
            <el-button
              link
              type="primary"
              @click="handleApply(scope.row)"
              v-if="scope.row.invoiceStatus === 'apply'"
            >申请开票</el-button>
            <el-button
              link
              type="primary"
              @click="handleLook(scope.row)"
            >查看</el-button>
            <el-button
              link
              type="primary"
              @click="handleDelete(scope.row)"
              v-if="scope.row.invoiceStatus === 'apply'"
            >删除</el-button>
            <el-button
              link
              type="primary"
              @click="handleExport(scope.row)"
              v-if="scope.row.invoiceStatus === 'pending'"
            >
              下载结算单</el-button>
            <el-button
              link
              type="primary"
              @click="handleStatement(scope.row)"
              v-if="scope.row.invoiceStatus === 'invoiced' && scope.row.statementStatus != 'settled'"
            >
              确认结算单</el-button>
            <el-button
              link
              type="primary"
              @click="handleLookInvoice(scope.row)"
              v-if="scope.row.invoiceStatus === 'invoiced' && scope.row.statementStatus != 'settled'"
            >
              查看发票</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />

    </Cards>

    <!-- 申请开票弹窗 -->
    <el-dialog
      v-model="invoiceOpen"
      :title="applyTitle"
      width="500"
    >
      <el-form :model="applyForm">
        <el-form-item
          label="备注"
          prop="invoiceRemark"
          label-width="80px"
          style="margin: 15px 0 30px 15px;"
        >
          <el-input
            v-model="applyForm.invoiceRemark"
            type="textarea"
            :rows="5"
            style="width: 280px;"
          />
        </el-form-item>
      </el-form>
      <div style="margin-left: 150px;">
        <el-button @click="cancel">取消</el-button>
        <el-button
          type="primary"
          @click="submitApplyFrom"
        >
          提交
        </el-button>
      </div>
    </el-dialog>

    <!-- 查看弹窗 -->
    <el-dialog
      v-model="lookOpen"
      title="查看"
      width="1000"
    >
      <div class="container2" v-if="invoicedStatement === 'invoiced'">
        <div class="btnBlue"></div>
        <span class="Word1">开票信息</span>
        <div
          :class="{ 'blueBackGround': invoiceData[0]?.fileUrl?.endsWith('.pdf') }"
          :style="{ width: invoiceData[0]?.fileUrl?.endsWith('.pdf') ? '60%' : '80px', height: invoiceData[0]?.fileUrl?.endsWith('.pdf') ? '30px' : '50px' }"
        >
        <div style="margin-left: 50px;">
          <!-- PDF 预览 -->
          <template v-if="invoiceData[0]?.fileUrl?.endsWith('.pdf')">
            <el-link
              :href="invoiceData[0].fileUrl"
              :underline="false"
              target="_blank"
              style="margin-top: 8px; margin-left: -40px;"
            >
              <span style="text-align: center; margin-left: 40px;">
                {{ invoiceData[0].fileName }}
              </span>
            </el-link>
          </template>

          <!-- 图片预览 -->
          <template v-else-if="invoiceData[0]?.fileUrl">
            <el-image
              :src="invoiceData[0].fileUrl"
              :preview-teleported="true"
              :preview-src-list="[invoiceData[0].fileUrl]"
              :initial-index="0"
              style="width: 80px; height: 50px; margin-left: 78px;"
            />
          </template>
        </div>
      </div>

  </div>
  <div class="container">
    <div class="btnBlue"></div>
    <span class="Word">基本信息</span>
    <div class="order">结算订单号：{{ statementNo }}</div>
  </div>
  <div class="container1">
    <div class="btnBlue"></div>
    <span class="Word">订单信息</span>
  </div>
  <el-table
    :data="orderData"
    :header-cell-style="{ background: '#EDF4FD !important', color: '#666666', textAlign: 'center' }"
    :cell-style="{ textAlign: 'center' }"
    highlight-current-row
    style="width: 95%;margin: 15px 0 0 30px"
  >

    <el-table-column
      prop="orderNo"
      label="订单号"
      width="200"
    />
    <el-table-column
      prop="shouldPay"
      label="应付金额"
    />
    <el-table-column
      prop="total"
      label="商品总额"
    />
    <el-table-column
      prop="address"
      label="操作"
    >
      <template #default="scope">
        <el-button
          link
          @click="handleOrder(scope.row)"
        >查看</el-button>
      </template>
    </el-table-column>
  </el-table>
  <template #footer>
    <div class="dialog-footer">
      <el-button
        @click="cancel"
        style="margin:10px 450px 0 0"
      >取消</el-button>
    </div>
  </template>
  </el-dialog>

  <!-- 查看发票弹窗 -->
  <el-dialog
    v-model="lookInvoiceOpen"
    title="查看发票"
    width="1000"
  >
    <el-button
      link
      icon="QuestionFilled"
      type="primary"
      style="margin: 0 0 15px 850px;"
      @click="reOpen()"
    >重新开票</el-button>

    <el-table
      :data="invoiceData"
      :header-cell-style="{ background: '#EDF4FD !important', color: '#666666', textAlign: 'center' }"
      :cell-style="{ textAlign: 'center' }"
      style="width: 100%"
    >
      <el-table-column
        prop="invoiceCode"
        label="发票编码"
      />
      <el-table-column
        prop="fileUrl"
        label="发票电子凭证"
      >
        <!-- PDF预览 -->
        <template v-if="invoiceData[0].fileUrl.endsWith('.pdf')">
          <el-link
            :href="invoiceData[0].fileUrl"
            :underline="false"
            target="_blank"
            style="margin-top: 8px;margin-left: -40px;"
          >
            <span style="text-align: center;margin-left: 40px;"> {{ invoiceData[0].fileName }} </span>
          </el-link>
        </template>
        <!-- 图片预览 -->
        <template v-else>
          <el-image
            :src="invoiceData[0].fileUrl"
            :preview-teleported="true"
            :preview-src-list="invoiceData[0].fileUrl"
            :initial-index="0"
            style="width: 80;height: 50px;margin-left: 78px;"
          />
        </template>

      </el-table-column>
      <el-table-column label="操作">
        <template #default="scope">
          <el-button
            link
            @click="uploadDown(scope.row)"
            type="primary"
          >下载</el-button>
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">取消</el-button>
      </div>
    </template>
  </el-dialog>
  <!-- 里层查看弹窗 -->
  <el-dialog
    v-model="inLookOpen"
    title="结算"
    width="1300"
  >
    <div class="top">
      <div class="in_top">
        订单编号：{{orderItemData.length > 0 ? orderItemData[0].orderNo : null}}
      </div>
      <div class="in_top">
        订单类型：
        <el-tag>
          {{ getOptionLabel(orderStatus,orderItemData.length > 0 ? orderItemData[0].orderType : null) }}
        </el-tag>
      </div>
      <div class="in_top">
        书商名称：{{ orderItemData.length > 0 ? orderItemData[0].merchanName : null}}
      </div>
      <div class="in_top">
        学校名称：{{ orderItemData.length > 0 ? orderItemData[0].schoolName : null}}
      </div>
      <div class="in_top">
        地区名称：{{ orderItemData.length > 0 ? orderItemData[0].areaName : null}}
      </div>
      <div class="in_top">
        经办人：{{ orderItemData.length > 0 ? orderItemData[0].nickName : null}}
      </div>
    </div>
    <div class="container1">
      <div class="btnBlue"></div>
      <span class="Word">订单信息</span>
    </div>
    <el-table
      :data="orderItemData"
      style="width: 95%;margin:10px 0 0 30px;"
      :header-cell-style="{ background: '#EDF4FD !important', color: '#666666', textAlign: 'center' }"
        :cell-style="{ textAlign: 'center' }"
    >
      <el-table-column
        prop="bookName"
        label="教材名称"
      />
      <el-table-column
        prop="isbn"
        label="ISBN/ISSN"
      >
        <template #default="{ row }">
          {{ row.isbn || row.issn || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="priceSale"
        label="售价"
      />
      <el-table-column
        prop="bookQuantity"
        label="数量"
      />
      <el-table-column
        prop="discountPercentage"
        label="折扣"
      />
      <el-table-column
        prop="shouldPay"
        label="总计"
      />
    </el-table>
    <div style="margin: 15px 0 0 950px;">
      <div>商品总额 : {{ sumtotal }}</div>
      <div style="margin-top: 15px;">应付金额: {{ sumShuoldAmount }}</div>
    </div>
    <!-- <div class="dialog-footer">
      <el-button @click="inLookOpen= false">取消</el-button>
    </div> -->
  </el-dialog>
  </div>
</template>

<script setup name="Statement">
import {
  listStatement,
  getStatement,
  delStatement,
  addStatement,
  updateStatement,
  changeStatementStatus,
  getStatementInvoice,
  getOrderItem,
  changeInvoice,
} from "@/api/shop/statement";
import { listStatementOrder } from "@/api/shop/statementOrder";
import { useRouter } from "vue-router";
import { getMerchantNoPage } from "@/api/shop/merchant";
import { listSchoolNoPage } from "@/api/basic/school.js";
import { getSaleAreaNoPage } from "@/api/shop/area";
// import { getOrderItem } from "@/api/shop/orderReview";
import { onMounted } from 'vue';
import {
  getOptionLabel,
  statementStatus,
  invoiceStatus,
  getOptionDesc,
  orderStatus,
} from "@/utils/optionUtil";
import { addInvoiceApply, addApply } from "@/api/shop/apply";
import { TextRun } from "docx";
const { proxy } = getCurrentInstance();

const areaList = ref([]);
//显示发票详情 invoice
const invoicedStatement = ref('')
const statementList = ref([]);
// 书商list
const merchanList = ref([]);
//学校list
const schoolList = ref([]);
// 1申请开票 2待开票 3已开票
const status = ref(1);
// 申请开票
const invoiceOpen = ref(false);
// 查看发票
const lookInvoiceOpen = ref(false);
// 查看
const lookOpen = ref(false);
// 里层查看
const inLookOpen = ref(false);
const open = ref(false);
// 订单详情列表
const orderData = ref([]);
// 订单明细
const orderItemData = ref([]);
//发票列表
const invoiceData = ref([]);
// 书商id =>传给后台
const merchantId = ref(1);
//结算单id
const statementId = ref(1);
// 学校id
const schoolId = ref(1);
const lookStatementId = ref(1);
// 结算单号
const statementNo = ref(1);
const sumtotal = ref(1);
const sumShuoldAmount = ref(1);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const applyTitle = ref("");
const router = useRouter();

const data = reactive({
  form: {},
  applyForm: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    statementName: null,
    statementNo: null,
    statementStatus: null,
    invoiceStatus: null,
    startTime: null,
    endTime: null,
    createTimeList: [],
  },
  rules: {},
});

const { queryParams, form, rules, applyForm } = toRefs(data);
onMounted(() => {
  getList()
})

onActivated(() => {
getList()
});

/** 查询结算单列表 */
function getList() {
  loading.value = true;
  listStatement(queryParams.value).then((response) => {
    statementList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  invoiceOpen.value = false;
  open.value = false;
  lookOpen.value = false;
  lookInvoiceOpen.value = false;
  inLookOpen.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    statementId: null,
    statementName: null,
    statementNo: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    statementStatus: null,
    invoiceStatus: null,
  };
  proxy.resetForm("statementRef");
}

// 对账按钮
function handleInvoice() {
  router.replace({ path: "invoiceIndex" });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.startTime = queryParams.value.createTimeList[0];
  queryParams.value.endTime = queryParams.value.createTimeList[1];
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  queryParams.value.createTimeList = [];
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.statementId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加结算单";
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["statementRef"].validate((valid) => {
    if (valid) {
      if (form.value.statementId != null) {
        updateStatement(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addStatement(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _statementIds = row.statementId || ids.value;
  proxy.$modal
    .confirm("是否确认删除结算单")
    .then(function () {
      return delStatement(_statementIds);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport(row) {
  proxy.download(
    "shop/statement/export",
    {
      ...queryParams.value,
      statementId: row.statementId,
    },
    `statement_${new Date().getTime()}.xlsx`
  );
}

// 查询书商下拉框
function getMerchantList() {
  getMerchantNoPage().then((res) => {
    merchanList.value = res.data;
  });
}
getMerchantList();

// 查询学校列表（下拉框）
function getSchoolList() {
  listSchoolNoPage().then((res) => {
    schoolList.value = res.data;
  });
}
getSchoolList();

//地区列表（下拉框）
function getSaleAreaList() {
  getSaleAreaNoPage().then((res) => {
    areaList.value = res.data;
  });
}
getSaleAreaList();
// 申请开票
function handleApply(row) {
  console.log(row);
  applyForm.value.invoiceRemark = null;
  merchantId.value = row.merchantId;
  statementId.value = row.statementId;
  schoolId.value = row.schoolId;
  statementNo.value = row.statementNo;
  invoiceOpen.value = true;
  applyTitle.value = "申请开票";
}

// 提交发票
function submitApplyFrom() {
  const merchantId_ = merchantId.value;
  const statementId_ = statementId.value;
  const schoolId_ = schoolId.value;
  const statementNo_ = statementNo.value;
  applyForm.value.schoolId = schoolId_;
  applyForm.value.statementNo = statementNo_;
  applyForm.value.merchantId = merchantId_;
  applyForm.value.statementId = statementId_;
  addInvoiceApply(applyForm.value).then((response) => {
    proxy.$modal.msgSuccess("提交申请成功");
    invoiceOpen.value = false;
    getList();
  });
}

// 查看
function handleLook(row) {
  console.log(row);
  invoicedStatement.value = row.invoiceStatus
  const statementId_ = row.statementId;
  getStatementInvoice(statementId_).then((res) => {
    invoiceData.value = res.data;
  });
  listStatementOrder(statementId_).then((res) => {
    orderData.value = res.rows;
    statementNo.value = res.rows[0].statementNo;
  });
  lookOpen.value = true;
}

// 确认结算单
function handleStatement(row) {
  const statementId_ = row.statementId;
  proxy.$modal
    .confirm("确认已经收到付款，确认结算?")
    .then(function () {
      return changeStatementStatus(statementId_);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("结算成功");
    })
    .catch(() => {});
}
//查看发票
function handleLookInvoice(row) {
  lookStatementId.value = row.statementId;
  const statementId_ = row.statementId;
  getStatementInvoice(statementId_).then((res) => {
    invoiceData.value = res.data;
    lookInvoiceOpen.value = true;
  });
}

// 查看订单明细
function handleOrder(row) {
  const orderId_ = row.orderId;
  getOrderItem(orderId_).then((res) => {
    orderItemData.value = res.rows || [];
    // 商品总额
    sumtotal.value = orderItemData.value.reduce((acc, item) => {
      return acc + item.total;
    }, 0);
    //应付金额
    sumShuoldAmount.value = orderItemData.value.reduce((acc, item) => {
      return acc + item.shouldPay;
    }, 0);
    inLookOpen.value = true;
  });
}

// 下载发票文件
async function uploadDown(row) {
  const fileUrl_ = row.fileUrl;
  const fileName_ = row.fileName;
  try {
    const response = await fetch(fileUrl_);
    if (!response.ok) throw new Error("网络响应不正常");
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = fileName_;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error("下载失败:", error);
  }
}

function reOpen() {
  proxy.$modal
    .confirm("确认重新开票?")
    .then(function () {
      return changeInvoice(lookStatementId.value);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("已提交申请");
    })
    .catch(() => {});
}
getList();
</script>
<style lang="scss" scoped>
.tool {
  padding: 20px 0 0;
}

.top20 {
  margin-top: 20px;
}

.pagination {
  float: right;
  margin: 20px 0;
}
.container {
  display: flex;
  width: 100%;
}
.btnBlue {
  width: 5px;
  height: 25px;
  background-color: cornflowerblue;
  margin: 15px 0 0 30px;
  flex-direction: row;
}
.Word {
  color: cornflowerblue;
  margin: 18px 10px 0 15px;
  width: 100px;
  height: 30px;
}
.Word1 {
  color: cornflowerblue;
  margin: 18px 10px 0 15px;
  width: 100px;
  height: 30px;
  position: relative;
  left: 30px;
  top: -22px;
}
.order {
  margin: 70px 0 0 -35px;
}
.container1 {
  display: flex;
  width: 100%;
  margin-top: 15px;
}
.container2 {
  display: flex;
  width: 100%;
  margin-top: 10px;
  display: inline-block;
}
.blueBackGround {
  width: 60%;
  background: #EDF4FD !important;
  color: #666666;
  height: 30px;
  flex-direction: column;
  position: relative;
  top: 10px;
  left: 70px;
  margin-bottom: 30px;
  transition: 15%;
}
.top {
  display: flex;
  margin-left: 20px;
}
.in_top {
  // flex-direction: row;
  margin-right: 50px;
}
</style>