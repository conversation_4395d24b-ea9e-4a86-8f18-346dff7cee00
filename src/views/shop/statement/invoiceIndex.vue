<template>
  <div class="app-container">
    <Cards>
      <div class="tool">
        <el-form
          :model="queryParams"
          ref="queryRef"
          :inline="true"
          label-width="68px"
        >
          <el-form-item
            prop="orderNo"
            label="订单号"
          >
            <el-input
              v-model="queryParams.orderNo"
              placeholder="请输入订单号"
              clearable
              style="width: 240px"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item
            prop="merchanName"
            label="书商名称"
          >
            <el-select
              v-model="queryParams.merchanName"
              placeholder="请选择书商名称"
              style="width: 240px"
            >
              <el-option
                v-for="item in merchanList"
                :key="item.merchantId"
                :label="item.merchanName"
                :value="item.merchanName"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            label="筛选时间"
          >
            <el-date-picker
              v-model="queryParams.createTimeList"
              type="daterange"
              value-format="YYYY-MM-DD"
              range-separator="到"
              start-placeholder="请选择开始日期"
              end-placeholder="请选择结束日期"
            />
          </el-form-item>
          <el-form-item
            prop="schoolName"
            label="学校名称"
          >
            <el-select
              v-model="queryParams.schoolName"
              placeholder="请选择学校名称"
              style="width: 240px"
            >
              <el-option
                v-for="item in schoolList"
                :key="item.schoolId"
                :label="item.schoolName"
                :value="item.schoolName"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            prop="areaName"
            label="地区名称"
          >
            <el-select
              v-model="queryParams.areaName"
              placeholder="请选择地区名称"
              @change="changeArea(queryParams)"
              style="width: 240px"
              
            >
              <el-option
                v-for="item in areaList"
                :key="item.areaId"
                :label="item.areaName"
                :value="item.areaName"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            prop="nickName"
            label="经办人"
          >
            <el-select
              v-model="queryParams.nickName"
              placeholder="请选择经办人"
              style="width: 240px"
              
            >
              <el-option
                v-for="item in memberList"
                :key="item.memberId"
                :label="item.nickName"
                :value="item.nickName"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              icon="Search"
              @click="handleQuery"
            >搜索</el-button>
            <el-button
              icon="Refresh"
              @click="resetQuery"
            >重置</el-button>
          </el-form-item>
        </el-form>

        <el-row
          :gutter="10"
          class="mb8"
          style="display: flex;justify-content: end;"
        >
          <el-col :span="1.5">
            <el-button
              type="warning"
              @click="handlePayment"
              :disabled = "status === 1"
            >创建结算单</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type='success' @click = "handleExport">导出对账订单</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="primary"
              @click="handBack"
            >返回</el-button>
          </el-col>
        </el-row>
      </div>
    </Cards>

    <Cards class="top20">
      <div class="container">
        <div
          class="leftTable"
          :style="status === 1?'width: 60%':'width:100%'"
        >
          <el-table
            v-loading="loading"
            :data="invoiceList"
            ref="statementRef"
            @selection-change="handleSelectionChange"
            :destroy-on-close="true"
            :header-cell-style="{ background: '#EDF4FD !important', color: '#666666', textAlign: 'center' }"
            :cell-style="{ textAlign: 'center' }"
          >
              <el-table-column
                type="selection"
                align="center"
                v-if="isShow"
              />
            <el-table-column
              label="订单号"
              align="center"
              prop="orderNo"
              width="200"
              fixed
            />
            <el-table-column
              label="采购数量"
              align="center"
              prop="payTotal"
              width="140"
            />
            <el-table-column
              label="已使用购书码"
              align="center"
              prop="useCode"
              width="140"
            />
            <el-table-column
              label="剩余购书码"
              align="center"
              prop="noUseCode"
              width="140"
            />
            <el-table-column
              label="商品总额"
              align="center"
              prop="goodsTotals"
              width="140"
            />
            <el-table-column
              label="应付金额"
              align="center"
              prop="shouldPay"
              width="140"
            />
            <el-table-column
              label="书商名称"
              align="center"
              prop="merchanName"
              width="140"
            >
            <template #default = {row}>
                {{ row.merchanName || '--' }}
            </template>
            </el-table-column>
            <el-table-column
              label="学校名称"
              align="center"
              prop="schoolName"
              width="140"
            />
            <el-table-column
              label="订单类型"
              align="center"
              prop="orderType"
              width="140"
            >
              <template #default="scope">
                <el-tag>
                  {{ getOptionDesc(bookOrderType,scope.row.orderType) }}
                </el-tag>
              </template>

            </el-table-column>
            <el-table-column
              label="经办人"
              align="center"
              prop="nickName"
              width="140"
            />
            <el-table-column
              label="地区名称"
              align="center"
              prop="areaName"
              width="140"
            />
            <el-table-column
              label="采购时间"
              align="center"
              prop="createTime"
              width="200"
            />

            <el-table-column
              label="操作"
              align="center"
              class-name="small-padding fixed-width"
              fixed="right"
            >
              <template #default="scope">
                <el-button
                  link
                  type="primary"
                  icon="Edit"
                  @click="handleLook(scope.row)"
                >查看</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div
          class="rightTable"
          v-show="status == 1"
        >
          <div class="rightTableWrapper">
            <el-table
              :data="tableData"
              :header-cell-style="{ background: '#EDF4FD !important', color: '#666666', textAlign: 'center' }"
              :cell-style="{ textAlign: 'center' }"
              height="90%"
            >
              <el-table-column
                prop="orderNo"
                label="订单号"
              />
              <el-table-column
                prop="shouldPay"
                label="应付金额"
              />
              <el-table-column
                prop="shouldPay"
                label="计划回款"
              />
            </el-table>
          </div>
          <div class="rightTableActions">
            <el-button @click="statementCancel">取消</el-button>
            <el-button
              @click="submitStatement"
              v-hasPermi="['shop:statement:add']"
            >确认</el-button>
          </div>
        </div>
      </div>

      <pagination
        v-show="total>0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />

    </Cards>

    <el-dialog
      v-model="LookOpen"
      title="查看"
      width="1000"
    >
      <div class="top">
        <div class="in_top">
          订单号：{{ itemData.length > 0 ? itemData[0].orderNo : null}}
        </div>

        <div class="in_top">
          书商名称：{{ itemData.length > 0 && itemData[0].merchanName ? itemData[0].merchanName : '--' }}
        </div>
        <div class="in_top">
          学校名称：{{  itemData.length > 0 ? itemData[0].schoolName : null}}
        </div>

      </div>
      <el-table
        :data="itemData"
        style="width: 100%;margin:30px 0 0 0px;"
        :header-cell-style="{ background: '#EDF4FD !important', color: '#666666', textAlign: 'center' }"
        :cell-style="{ textAlign: 'center' }"
      >
        <el-table-column
          prop="cover"
          label="封面图"
        >
          <template #default="scope">
            <el-image
              :src="scope.row.cover"
              width="120"
              height="70"
              :preview-src-list="[scope.row.cover]"
              :zoom-rate="1.2"
              :max-scale="7"
              :min-scale="0.2"
              :initial-index="4"
              fit="cover"
              preview-teleported
            />
          </template>
        </el-table-column>
        <el-table-column
          prop="bookName"
          label="教材名称"
        />
        <el-table-column
          prop="isbn"
          label="ISBN/ISSN"
        >
          <template #default="{ row }">
            {{ row.isbn || row.issn || '-' }}
          </template>
        </el-table-column>

        <el-table-column
          prop="bookQuantity"
          label="数量"
        />
        <el-table-column
          prop="useCode"
          label="已使用购数码"
        />
        <el-table-column
          prop="noUseCode"
          label="剩余购数码"
        />
        <el-table-column
          prop="total"
          label="总计"
        >
        <template #default="scope">
                {{ (scope.row.bookQuantity * scope.row.priceSale ) }}
              </template>
        </el-table-column>
      </el-table>
      <div style="margin: 15px 0 0 750px;">
        <!-- <div>商品总额 : {{ orderItemData.length > 0 ? orderItemData[0].total : null }}</div> -->
        <div>应付金额: {{ itemData.length > 0 ? itemData[0].shouldPay : null }}</div>
      </div>
      <!-- <div class="dialog-footer">
      <el-button @click="inLookOpen= false">取消</el-button>
    </div> -->
    </el-dialog>
  </div>
</template>
  
  <script setup name="Statement">
import {
  listStatement,
  getStatement,
  delStatement,
  addStatement,
  updateStatement,
  reconcileList,
  getReconcileListById,
} from "@/api/shop/statement";
import { listSchoolNoPage } from "@/api/basic/school.js";
import { getMerchantNoPage } from "@/api/shop/merchant";
import { getSaleAreaNoPage } from "@/api/shop/area";
import { memberListNoPage } from "@/api/shop/member";
import {
  statementStatus,
  invoiceStatus,
  orderStatus,
  bookOrderType,
  getOptionDesc,
} from "@/utils/optionUtil";
import { useRouter } from "vue-router";
import { Logger } from "sass";
const { proxy } = getCurrentInstance();

//学校list
const schoolList = ref([]);
// 书商list
const merchanList = ref([]);
//地区list
const areaList = ref([]);
//经办人list
const memberList = ref([]);
const statementList = ref([]);
//是否显示结算table
const status = ref("");
const shouldPayShow = ref(1);
const LookOpen = ref(false);
const itemData = ref([]);
const tableData = ref([]);
const router = useRouter();
const invoiceList = ref([]);
const open = ref(false);
const loading = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const isShow = ref(false)
const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    statementName: null,
    statementNo: null,
    statementStatus: null,
    invoiceStatus: null,
    startTime: null,
    endTime: null,
    createTimeList:[],
  },
  rules: {},
});

const { queryParams, form, rules } = toRefs(data);

/** 查询对账单列表 */
function getList() {
  loading.value = true;
  reconcileList(queryParams.value).then((response) => {
    invoiceList.value = response.rows;
    total.value = response.total;
    isShow.value = false
    loading.value = false;
  });

}
//查询结算单列表
function getStatementList() {
  listStatement().then((response) => {
    statementList.value = response.rows;
  });
}
// 表单重置
function reset() {
  form.value = {
    statementId: null,
    statementName: null,
    statementNo: null,
    bookName:null,
    issn:null,
    isbn:null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    statementStatus: null,
    invoiceStatus: null,
  };
  proxy.resetForm("statementRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.startTime = queryParams.value.createTimeList[0];
  console.log(queryParams.value.startTime);

  queryParams.value.endTime = queryParams.value.createTimeList[1];
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  queryParams.value.createTimeList = [];
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  if (status.value === 1) {
    tableData.value = selection;
  }
  ids.value = selection.map((item) => item.orderId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _statementIds = row.statementId || ids.value;
  proxy.$modal
    .confirm('是否确认删除结算单编号为"' + _statementIds + '"的数据项？')
    .then(function () {
      return delStatement(_statementIds);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  isShow.value = true;
  proxy.download(
    "shop/statement/exportReconcile",
    {
      ...queryParams.value,
      ids:ids.value
    },
    `对账单${new Date().getTime()}.xlsx`
  );
}
// 查询学校列表（下拉框）
function getSchoolList() {
  listSchoolNoPage().then((res) => {
    schoolList.value = res.data;
  });
}
getSchoolList();

// 查询书商下拉框
function getMerchantList() {
  getMerchantNoPage().then((res) => {
    merchanList.value = res.data;
  });
}
getMerchantList();
//地区列表（下拉框）
function getSaleAreaList() {
  getSaleAreaNoPage().then((res) => {
    areaList.value = res.data;
  });
}
getSaleAreaList();

//监听地区
function changeArea(queryParams) {
  console.log(queryParams);
  memberListNoPage(queryParams).then((res) => {
    console.log(res);
    memberList.value = res.data;
  });
}

//返回按钮
function handBack() {
  router.replace({ path: "statement" })
}

//是否显示结算table
function handlePayment() {
  tableData.value = []
  status.value = 1;
  isShow.value = true;
}
function statementCancel() {
  proxy.$refs.statementRef.clearSelection();
  tableData.value = [];
  isShow.value = false;
  status.value = 2;
}

//提交结算单
function submitStatement() {
  console.log(tableData.value);
  console.log(tableData.value[0].orderType)
  if(tableData.value.length === 0){
    proxy.$modal.msgError("请选择订单");
    return;
  }
  // 验证是否都为同一书商或者同一学校
  const checkOrderType = checkParam(tableData.value, 1);
  if(!checkOrderType){
    proxy.$modal.msgError("请选择同一书商或者同一学校的订单");
    return;
  }
  // 验证是否为同一书商或者同一学校
  if (tableData.value[0].orderType === 5) {
    const checkMeType = checkParam(tableData.value, 2);
    if(!checkMeType){
      proxy.$modal.msgError("请选择同一书商的订单");
      return;
    }
  } else {
    // 验证是否为同一书商或者同一学校
    const checkSchoolType = checkParam(tableData.value, 3);
    if(!checkSchoolType){
      proxy.$modal.msgError("请选择同一学校的订单");
      return;
    }
  }


  const merchantId_ = [...new Set(tableData.value.map(item => Number(item.merchantId)))]
  const _merchantId = merchantId_[0]
  const payTotal_ = tableData.value.reduce((accumulator, item) => {
    return accumulator + item.payTotal;
  }, 0);

  const goodsTotals_ = tableData.value.reduce((accumulator, item) => {
    return accumulator + item.goodsTotals;
  }, 0);

  const shouldPay_ = tableData.value.reduce((accumulator, item) => {
    return accumulator + item.shouldPay;
  }, 0);

  const merchanName_ = [
    ...new Set(tableData.value.map((item) => item.merchanName)),
  ].join(",");

  const schoolName_ = tableData.value.map((item) => item.schoolName).join(",");
  const orderIds = tableData.value.map((item) => item.orderId);

  const StatementData = {
    bookQuantity: payTotal_,
    goodsTotals: goodsTotals_,
    payAmount: shouldPay_,
    merchanName: merchanName_,
    schoolName: schoolName_,
    orderIds: orderIds,
    merchantId:tableData.value[0].merchantId,
    orderType:tableData.value[0].orderType,
    orderTypes:tableData.value[0].orderTypes,
    schoolId: tableData.value[0].schoolId,
  };
  addStatement(StatementData).then((response) => {
    proxy.$modal.msgSuccess("新增成功");
    open.value = false;
    getList();
  });
  status.value = 2
}

function checkParam(list, type){
  const orderType = new Set(list.map(item => item.orderType));
  if(type === 1){
    // 必须都是书商订单或者学校订单
    return orderType.size === 1;
  } else if(type === 2 && orderType.size === 1){
    // 必须都是同一书商订单
    const merchantId = new Set(list.map(item => item.merchantId));
    return merchantId.size === 1;
  } else if(type === 3 && orderType.size === 1){
    // 必须都是同一学校订单
    const schoolId = new Set(list.map(item => item.schoolId));
    return schoolId.size === 1;
  }
}

// 查看
function handleLook(row) {
  LookOpen.value = true;
  getReconcileListById(row.orderId).then((res) => {

    itemData.value = res.rows;
    console.log(res.rows);
    shouldPayShow.value = res.rows.reduce(
  (sum, item) => (item.discount * item.priceSale * item.bookQuantity),
  0
);
  
  })
  
}
getList();
</script>
  <style lang="scss" scoped>
.tool {
  padding: 20px 0 0;
}

.top20 {
  margin-top: 20px;
}
.top {
  display: flex;
}
.in_top {
  flex-direction: row;
  padding-right: 130px;
}

.pagination {
  float: right;
  margin: 20px 0;
}
.container {
  display: flex;
  height: auto; 
}

.leftTable {
  padding-right: 10px;
}

.rightTable {
  flex: 1;
  padding-left: 10px;
  display: flex;
  flex-direction: column;
}

.rightTableWrapper {
  flex: 1;
  overflow: hidden;
}

.rightTableActions {
  padding-top: 10px;
  text-align: left;
}
</style>